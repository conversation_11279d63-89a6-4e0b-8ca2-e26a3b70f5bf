# Mürşid - Streamlit Web Uygulaması

Bu proje, <PERSON><PERSON><PERSON> tabanlı "Mürşid" mobil uygulamasının Python Streamlit ile web versiyonudur. Aynı tasarım ve işlevselliği koruyarak vatandaşların ihlalleri bildirmesini ve ödül kazanmasını sağlar.

## 🚀 Özellikler

### Kullanıcı Özellikleri
- **🔐 Güvenli Giriş**: E-posta ve şifre ile demo giriş sistemi
- **📸 İhbar Oluşturma**: Fotoğraf/video yükleme ile ihbar
- **🏷️ İhlal Türleri**: 6 farklı ihlal kategorisi
  - 🚗 Trafik İhlalleri
  - 🗑️ Temizlik İhlalleri  
  - ⚠️ Taciz Olayları
  - 🚨 Şiddet Olayları
  - 🔊 Gürültü Kirliliği
  - 🅿️ Park İhlalleri
- **📍 Konum Entegrasyonu**: Otomatik ve manuel konum belirleme
- **📋 İhbar Takibi**: G<PERSON>nderilen ihbarların durumunu takip
- **💰 Kazanç Sistemi**: Onaylanan ihbarlardan %20 ödül
- **💳 Cüzdan**: Kazançları görüntüleme ve para çekme
- **👤 Profil Yönetimi**: Kullanıcı bilgileri ve ayarlar

### Teknik Özellikler
- **🐍 Python + Streamlit**: Modern web framework
- **🎨 Material Design**: Orijinal Flutter tasarımına uygun renkler
- **🌐 Türkçe Dil Desteği**: Tam Türkçe arayüz
- **📱 Responsive Design**: Mobil ve masaüstü uyumlu
- **🔄 Real-time Updates**: Anlık veri güncellemeleri
- **💾 Session Management**: Oturum yönetimi

## 🛠️ Kurulum

### Gereksinimler
- Python 3.8 veya üzeri
- pip (Python paket yöneticisi)

### Adımlar

1. **Projeyi klonlayın**
```bash
git clone <repository-url>
cd مرشد
```

2. **Sanal ortam oluşturun (önerilen)**
```bash
python -m venv venv
# Windows
venv\Scripts\activate
# Linux/Mac
source venv/bin/activate
```

3. **Bağımlılıkları yükleyin**
```bash
pip install -r requirements.txt
```

4. **Uygulamayı çalıştırın**
```bash
streamlit run app.py
```

5. **Tarayıcıda açın**
- Otomatik olarak açılmazsa: http://localhost:8501

## 📱 Kullanım

### Demo Giriş
- **E-posta**: Herhangi bir e-posta adresi (örn: <EMAIL>)
- **Şifre**: Herhangi bir şifre (örn: 123456)

### Ana Özellikler

#### 🏠 Ana Sayfa
- Hoş geldin mesajı
- Hızlı ihbar butonu
- İhlal türleri grid'i
- Kullanıcı istatistikleri
- Son ihbarlar özeti

#### 📝 Yeni İhbar
- Fotoğraf/video yükleme
- İhlal türü seçimi
- Detaylı açıklama
- Konum belirleme
- Doğrulama ve gönderim

#### 📋 İhbarlarım
- Tüm ihbarları listeleme
- Durum filtreleme
- İhlal türü filtreleme
- Detaylı ihbar görüntüleme
- İstatistikler

#### 💰 Cüzdan
- Bakiye bilgileri
- Para çekme işlemi
- İşlem geçmişi
- Kazanç istatistikleri

#### 👤 Profil
- Kullanıcı bilgileri
- Profil düzenleme
- Ayarlar
- Çıkış işlemi

## 🎨 Tasarım Sistemi

### Renkler
- **Primary**: #1976D2 (Mavi)
- **Secondary**: #388E3C (Yeşil)
- **İhlal Türü Renkleri**:
  - Trafik: #D32F2F (Kırmızı)
  - Temizlik: #795548 (Kahverengi)
  - Taciz: #7B1FA2 (Mor)
  - Şiddet: #E65100 (Turuncu)
  - Gürültü: #455A64 (Gri-Mavi)
  - Park: #5D4037 (Koyu Kahve)

### Bileşenler
- **Kartlar**: Yuvarlatılmış köşeler, gölge efekti
- **Butonlar**: Material Design tarzı
- **Formlar**: Temiz ve kullanıcı dostu
- **Navigasyon**: Sidebar menü sistemi

## 📁 Proje Yapısı

```
مرشد/
├── app.py                 # Ana uygulama dosyası
├── config.py             # Konfigürasyon ve sabitler
├── models.py             # Veri modelleri
├── utils.py              # Yardımcı fonksiyonlar
├── requirements.txt      # Python bağımlılıkları
├── pages/               # Sayfa dosyaları
│   ├── login.py         # Giriş sayfası
│   ├── home.py          # Ana sayfa
│   ├── new_report.py    # Yeni ihbar sayfası
│   ├── reports.py       # İhbarlar sayfası
│   ├── wallet.py        # Cüzdan sayfası
│   └── profile.py       # Profil sayfası
├── translations/        # Dil dosyaları
│   └── tr.json         # Türkçe çeviriler
└── data/               # Veri dosyaları (oluşturulacak)
```

## 🔧 Geliştirme

### Yeni Özellik Ekleme
1. İlgili sayfa dosyasını düzenleyin
2. Gerekirse `models.py`'ye yeni model ekleyin
3. `config.py`'de konfigürasyon güncelleyin
4. `utils.py`'ye yardımcı fonksiyon ekleyin

### Stil Değişiklikleri
- `config.py` dosyasındaki `get_custom_css()` fonksiyonunu düzenleyin
- Renk değişiklikleri için `AppColors` sınıfını güncelleyin

### Dil Desteği
- `translations/` klasörüne yeni dil dosyası ekleyin
- `config.py`'de dil seçeneklerini güncelleyin

## 🚀 Deployment

### Streamlit Cloud
1. GitHub'a push edin
2. [share.streamlit.io](https://share.streamlit.io) adresine gidin
3. Repository'yi bağlayın
4. `app.py` dosyasını seçin

### Heroku
```bash
# Procfile oluşturun
echo "web: streamlit run app.py --server.port=$PORT --server.address=0.0.0.0" > Procfile

# Deploy edin
git add .
git commit -m "Deploy to Heroku"
git push heroku main
```

### Docker
```dockerfile
FROM python:3.9-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8501

CMD ["streamlit", "run", "app.py", "--server.address", "0.0.0.0"]
```

## 📊 Demo Verileri

Uygulama demo verilerle gelir:
- **Kullanıcı**: Ahmet İssam (<EMAIL>)
- **İhbarlar**: 3 örnek ihbar (onaylı, incelemede, reddedildi)
- **Kazançlar**: ₺1,250 toplam, ₺850 kullanılabilir
- **İşlemler**: Ödül ve para çekme örnekleri

## 🔒 Güvenlik

- Demo uygulaması - gerçek güvenlik önlemleri yok
- Üretim için:
  - Gerçek authentication sistemi
  - Veritabanı entegrasyonu
  - HTTPS kullanımı
  - Input validation
  - Rate limiting

## 🤝 Katkıda Bulunma

1. Fork edin
2. Feature branch oluşturun (`git checkout -b feature/amazing-feature`)
3. Değişikliklerinizi commit edin (`git commit -m 'Add amazing feature'`)
4. Branch'inizi push edin (`git push origin feature/amazing-feature`)
5. Pull Request oluşturun

## 📞 Destek

- **Issues**: GitHub Issues kullanın
- **Dokümantasyon**: Bu README dosyası
- **Demo**: Canlı demo için Streamlit Cloud linkini kullanın

## 📄 Lisans

Bu proje MIT lisansı altında lisanslanmıştır.

## 🙏 Teşekkürler

- **Streamlit**: Harika web framework için
- **Material Design**: Tasarım ilhamı için
- **Flutter Ekibi**: Orijinal uygulama tasarımı için

---

**Not**: Bu uygulama demo amaçlıdır. Gerçek kullanım için backend API entegrasyonu ve güvenlik önlemleri eklenmelidir.
