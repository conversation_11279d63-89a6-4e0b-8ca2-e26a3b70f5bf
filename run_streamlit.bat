@echo off
echo ========================================
echo    Mursid - Streamlit Web Uygulamasi
echo ========================================
echo.

echo [1/3] Python kontrolu yapiliyor...
python --version >nul 2>&1
if errorlevel 1 (
    echo HATA: Python bulunamadi!
    echo Lutfen Python 3.8+ yukleyin: https://python.org
    pause
    exit /b 1
)
echo ✓ Python bulundu

echo.
echo [2/3] Bagimliliklar yukleniyor...
pip install -r requirements.txt
if errorlevel 1 (
    echo HATA: Bagimliliklar yuklenemedi!
    pause
    exit /b 1
)
echo ✓ Bagimliliklar yuklendi

echo.
echo [3/3] Streamlit uygulamasi baslatiliyor...
echo.
echo ========================================
echo  Uygulama baslatiliyor...
echo  Tarayicinizda otomatik acilacak
echo  Manuel acmak icin: http://localhost:8501
echo ========================================
echo.

streamlit run app.py

pause
