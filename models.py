# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Data Models
"""

from dataclasses import dataclass, field
from datetime import datetime
from typing import List, Optional, Dict, Any
import uuid
import json
from config import ViolationType, ReportStatus, UserRole

@dataclass
class Location:
    """Location data model"""
    latitude: float
    longitude: float
    address: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "latitude": self.latitude,
            "longitude": self.longitude,
            "address": self.address
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Location':
        return cls(
            latitude=data["latitude"],
            longitude=data["longitude"],
            address=data["address"]
        )

@dataclass
class User:
    """User data model"""
    id: str
    email: str
    full_name: str
    phone: str
    profile_image: Optional[str] = None
    role: UserRole = UserRole.USER
    total_earnings: float = 0.0
    available_balance: float = 0.0
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "email": self.email,
            "full_name": self.full_name,
            "phone": self.phone,
            "profile_image": self.profile_image,
            "role": self.role.value,
            "total_earnings": self.total_earnings,
            "available_balance": self.available_balance,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'User':
        return cls(
            id=data["id"],
            email=data["email"],
            full_name=data["full_name"],
            phone=data["phone"],
            profile_image=data.get("profile_image"),
            role=UserRole(data["role"]),
            total_earnings=data["total_earnings"],
            available_balance=data["available_balance"],
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )

@dataclass
class Report:
    """Report data model"""
    id: str
    user_id: str
    violation_type: ViolationType
    description: str
    media_urls: List[str]
    location: Location
    status: ReportStatus = ReportStatus.PENDING
    fine_amount: Optional[float] = None
    reward_amount: Optional[float] = None
    officer_notes: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "user_id": self.user_id,
            "violation_type": self.violation_type.value,
            "description": self.description,
            "media_urls": self.media_urls,
            "location": self.location.to_dict(),
            "status": self.status.value,
            "fine_amount": self.fine_amount,
            "reward_amount": self.reward_amount,
            "officer_notes": self.officer_notes,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Report':
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            violation_type=ViolationType(data["violation_type"]),
            description=data["description"],
            media_urls=data["media_urls"],
            location=Location.from_dict(data["location"]),
            status=ReportStatus(data["status"]),
            fine_amount=data.get("fine_amount"),
            reward_amount=data.get("reward_amount"),
            officer_notes=data.get("officer_notes"),
            created_at=datetime.fromisoformat(data["created_at"]),
            updated_at=datetime.fromisoformat(data["updated_at"])
        )

@dataclass
class Transaction:
    """Transaction data model for wallet"""
    id: str
    user_id: str
    report_id: Optional[str]
    amount: float
    transaction_type: str  # 'reward', 'withdrawal'
    description: str
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "user_id": self.user_id,
            "report_id": self.report_id,
            "amount": self.amount,
            "transaction_type": self.transaction_type,
            "description": self.description,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Transaction':
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            report_id=data.get("report_id"),
            amount=data["amount"],
            transaction_type=data["transaction_type"],
            description=data["description"],
            created_at=datetime.fromisoformat(data["created_at"])
        )

@dataclass
class Notification:
    """Notification data model"""
    id: str
    user_id: str
    title: str
    message: str
    notification_type: str  # 'report_update', 'reward', 'system'
    is_read: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "user_id": self.user_id,
            "title": self.title,
            "message": self.message,
            "notification_type": self.notification_type,
            "is_read": self.is_read,
            "created_at": self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Notification':
        return cls(
            id=data["id"],
            user_id=data["user_id"],
            title=data["title"],
            message=data["message"],
            notification_type=data["notification_type"],
            is_read=data["is_read"],
            created_at=datetime.fromisoformat(data["created_at"])
        )

# Helper functions for generating IDs
def generate_id() -> str:
    """Generate a unique ID"""
    return str(uuid.uuid4())

def generate_report_id() -> str:
    """Generate a report ID with prefix"""
    return f"RPT_{str(uuid.uuid4())[:8].upper()}"

def generate_user_id() -> str:
    """Generate a user ID with prefix"""
    return f"USR_{str(uuid.uuid4())[:8].upper()}"

def generate_transaction_id() -> str:
    """Generate a transaction ID with prefix"""
    return f"TXN_{str(uuid.uuid4())[:8].upper()}"

def generate_notification_id() -> str:
    """Generate a notification ID with prefix"""
    return f"NOT_{str(uuid.uuid4())[:8].upper()}"

# Demo data generators
def create_demo_user() -> User:
    """Create demo user"""
    return User(
        id="demo_user_1",
        email="<EMAIL>",
        full_name="Ahmet İssam",
        phone="+90 ************",
        total_earnings=1250.0,
        available_balance=850.0
    )

def create_demo_reports() -> List[Report]:
    """Create demo reports"""
    demo_location_1 = Location(
        latitude=41.0082,
        longitude=28.9784,
        address="Sultanahmet, Fatih, İstanbul"
    )
    
    demo_location_2 = Location(
        latitude=41.0425,
        longitude=29.0096,
        address="Beşiktaş, İstanbul"
    )
    
    demo_location_3 = Location(
        latitude=40.9923,
        longitude=29.0244,
        address="Kadıköy, İstanbul"
    )
    
    reports = [
        Report(
            id="RPT_001",
            user_id="demo_user_1",
            violation_type=ViolationType.TRAFFIC,
            description="Kırmızı ışıkta geçen araç tespit edildi. Plaka: 34 ABC 123",
            media_urls=["demo_image_1.jpg"],
            location=demo_location_1,
            status=ReportStatus.APPROVED,
            fine_amount=1000.0,
            reward_amount=200.0,
            created_at=datetime.now().replace(day=datetime.now().day-2),
            updated_at=datetime.now().replace(day=datetime.now().day-1)
        ),
        Report(
            id="RPT_002",
            user_id="demo_user_1",
            violation_type=ViolationType.CLEANLINESS,
            description="Sokağa çöp atma ihlali görüldü",
            media_urls=["demo_image_2.jpg"],
            location=demo_location_2,
            status=ReportStatus.IN_REVIEW,
            created_at=datetime.now().replace(hour=datetime.now().hour-5),
            updated_at=datetime.now().replace(hour=datetime.now().hour-5)
        ),
        Report(
            id="RPT_003",
            user_id="demo_user_1",
            violation_type=ViolationType.PARKING,
            description="Engelli park yerine park etme ihlali",
            media_urls=["demo_image_3.jpg"],
            location=demo_location_3,
            status=ReportStatus.REJECTED,
            officer_notes="Yeterli kanıt bulunamadı",
            created_at=datetime.now().replace(day=datetime.now().day-7),
            updated_at=datetime.now().replace(day=datetime.now().day-5)
        )
    ]
    
    return reports

def create_demo_transactions() -> List[Transaction]:
    """Create demo transactions"""
    return [
        Transaction(
            id="TXN_001",
            user_id="demo_user_1",
            report_id="RPT_001",
            amount=200.0,
            transaction_type="reward",
            description="Trafik ihlali raporu ödülü",
            created_at=datetime.now().replace(day=datetime.now().day-1)
        ),
        Transaction(
            id="TXN_002",
            user_id="demo_user_1",
            report_id=None,
            amount=-100.0,
            transaction_type="withdrawal",
            description="Para çekme işlemi",
            created_at=datetime.now().replace(day=datetime.now().day-3)
        )
    ]
