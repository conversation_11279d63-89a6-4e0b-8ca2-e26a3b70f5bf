# -*- coding: utf-8 -*-
"""
Mürşid - Desktop Application Launcher
تشغيل التطبيق في نافذة منفصلة
"""

import webview
import threading
import subprocess
import time
import sys
import os
import socket
from pathlib import Path

class StreamlitDesktopApp:
    def __init__(self):
        self.streamlit_process = None
        self.port = self.find_free_port()
        self.url = f"http://localhost:{self.port}"

    def find_free_port(self):
        """العثور على منفذ متاح"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            s.listen(1)
            port = s.getsockname()[1]
        return port

    def start_streamlit(self):
        """تشغيل خادم Streamlit في الخلفية"""
        try:
            # تحديد مسار ملف التطبيق
            app_path = Path(__file__).parent / "app.py"

            # تشغيل Streamlit
            cmd = [
                sys.executable, "-m", "streamlit", "run",
                str(app_path),
                "--server.port", str(self.port),
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false",
                "--server.enableCORS", "false",
                "--server.enableXsrfProtection", "false",
                "--theme.base", "light",
                "--theme.primaryColor", "#1f77b4",
                "--theme.backgroundColor", "#ffffff",
                "--theme.secondaryBackgroundColor", "#f0f2f6"
            ]

            self.streamlit_process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )

            print(f"🚀 تم تشغيل Streamlit على المنفذ {self.port}")

        except Exception as e:
            print(f"❌ خطأ في تشغيل Streamlit: {e}")
            return False

        return True

    def wait_for_streamlit(self, timeout=30):
        """انتظار تشغيل Streamlit"""
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                response = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = response.connect_ex(('localhost', self.port))
                response.close()

                if result == 0:
                    print("✅ Streamlit جاهز!")
                    return True

            except Exception:
                pass

            time.sleep(1)

        print("⏰ انتهت مهلة انتظار Streamlit")
        return False

    def create_window(self):
        """إنشاء نافذة التطبيق بحجم الهاتف"""
        try:
            # أحجام شاشات الهواتف المختلفة
            phone_sizes = {
                "iphone_se": (375, 667),      # iPhone SE
                "iphone_12": (390, 844),      # iPhone 12/13
                "iphone_14_pro": (430, 932),  # iPhone 14 Pro
                "samsung_s21": (360, 800),    # Samsung Galaxy S21
                "pixel_6": (411, 914)         # Google Pixel 6
            }

            # استخدام iPhone 12 كحجم افتراضي (الأكثر شيوعاً)
            default_size = phone_sizes["iphone_12"]
            phone_width = default_size[0]
            phone_height = default_size[1]

            print(f"📱 حجم النافذة: {phone_width}x{phone_height} (iPhone 12 style)")

            # إنشاء النافذة بحجم الهاتف
            window = webview.create_window(
                title="📱 مرشد - تطبيق الإبلاغ عن المخالفات",
                url=self.url,
                width=phone_width,
                height=phone_height,
                min_size=phone_sizes["iphone_se"],     # iPhone SE كحد أدنى
                resizable=True,
                fullscreen=False,
                minimized=False,
                on_top=False,
                shadow=True,
                focus=True
            )

            return window

        except Exception as e:
            print(f"❌ خطأ في إنشاء النافذة: {e}")
            return None

    def on_window_closed(self):
        """عند إغلاق النافذة"""
        print("🔄 إغلاق التطبيق...")

        if self.streamlit_process:
            try:
                self.streamlit_process.terminate()
                self.streamlit_process.wait(timeout=5)
                print("✅ تم إيقاف Streamlit")
            except Exception as e:
                print(f"⚠️ خطأ في إيقاف Streamlit: {e}")
                try:
                    self.streamlit_process.kill()
                except:
                    pass

    def run(self):
        """تشغيل التطبيق"""
        print("🚀 بدء تشغيل تطبيق مرشد...")

        # تشغيل Streamlit في thread منفصل
        streamlit_thread = threading.Thread(target=self.start_streamlit, daemon=True)
        streamlit_thread.start()

        # انتظار تشغيل Streamlit
        if not self.wait_for_streamlit():
            print("❌ فشل في تشغيل Streamlit")
            return

        # إنشاء النافذة
        window = self.create_window()
        if not window:
            print("❌ فشل في إنشاء النافذة")
            return

        print("🎉 تم تشغيل التطبيق بنجاح!")
        print(f"📱 النافذة متاحة على: {self.url}")

        try:
            # تشغيل النافذة
            webview.start(
                debug=False,
                http_server=False,
                func=None
            )
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف التطبيق بواسطة المستخدم")
        except Exception as e:
            print(f"❌ خطأ في تشغيل النافذة: {e}")
        finally:
            self.on_window_closed()

def main():
    """الدالة الرئيسية"""
    try:
        # التحقق من وجود الملفات المطلوبة
        if not Path("app.py").exists():
            print("❌ ملف app.py غير موجود!")
            input("اضغط Enter للخروج...")
            return

        # إنشاء وتشغيل التطبيق
        app = StreamlitDesktopApp()
        app.run()

    except Exception as e:
        print(f"❌ خطأ عام: {e}")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
