#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرشد - تطبيق نافذة منفصلة
تشغيل التطبيق في نافذة منفصلة وليس في المتصفح
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser
from pathlib import Path

def kill_existing_streamlit():
    """إيقاف أي عمليات streamlit موجودة"""
    try:
        if sys.platform == "win32":
            subprocess.run(["taskkill", "/f", "/im", "streamlit.exe"], 
                         capture_output=True, check=False)
        else:
            subprocess.run(["pkill", "-f", "streamlit"], 
                         capture_output=True, check=False)
    except:
        pass

def run_streamlit_app():
    """تشغيل تطبيق Streamlit"""
    try:
        # التأكد من وجود ملف التطبيق
        app_file = Path("app.py")
        if not app_file.exists():
            print("❌ ملف app.py غير موجود!")
            return False
        
        print("🚀 بدء تشغيل التطبيق...")
        
        # إيقاف أي عمليات موجودة
        kill_existing_streamlit()
        time.sleep(2)
        
        # تشغيل Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run", "app.py",
            "--server.port=8501",
            "--server.address=localhost",
            "--server.headless=true",
            "--browser.gatherUsageStats=false",
            "--theme.base=light"
        ]
        
        print("📱 تشغيل التطبيق في نافذة منفصلة...")
        
        # تشغيل العملية
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True,
            creationflags=subprocess.CREATE_NO_WINDOW if sys.platform == "win32" else 0
        )
        
        # انتظار حتى يبدأ الخادم
        print("⏳ انتظار بدء الخادم...")
        time.sleep(5)
        
        # فتح التطبيق في نافذة منفصلة
        url = "http://localhost:8501"
        
        if sys.platform == "win32":
            # فتح في نافذة Chrome منفصلة (إذا كان متوفراً)
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            chrome_found = False
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    try:
                        subprocess.Popen([
                            chrome_path,
                            "--app=" + url,
                            "--window-size=400,700",
                            "--window-position=100,100",
                            "--disable-web-security",
                            "--disable-features=VizDisplayCompositor"
                        ])
                        chrome_found = True
                        print("🌐 تم فتح التطبيق في نافذة Chrome منفصلة")
                        break
                    except:
                        continue
            
            if not chrome_found:
                # استخدام المتصفح الافتراضي
                webbrowser.open(url)
                print("🌐 تم فتح التطبيق في المتصفح الافتراضي")
        else:
            # للأنظمة الأخرى
            webbrowser.open(url)
            print("🌐 تم فتح التطبيق في المتصفح")
        
        print("✅ التطبيق يعمل الآن!")
        print(f"🔗 الرابط: {url}")
        print("📱 حجم النافذة: 400x700 (حجم هاتف)")
        print("🛑 اضغط Ctrl+C لإيقاف التطبيق")
        
        # انتظار إيقاف العملية
        try:
            process.wait()
        except KeyboardInterrupt:
            print("\n🛑 إيقاف التطبيق...")
            process.terminate()
            process.wait()
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في تشغيل التطبيق: {e}")
        return False

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("🛡️  مرشد - تطبيق الإبلاغ عن المخالفات")
    print("📱 تطبيق نافذة منفصلة")
    print("=" * 50)
    
    # التحقق من Python
    print(f"🐍 Python: {sys.version}")
    
    # التحقق من Streamlit
    try:
        import streamlit
        print(f"⚡ Streamlit: {streamlit.__version__}")
    except ImportError:
        print("❌ Streamlit غير مثبت!")
        print("💡 قم بتثبيته: pip install streamlit")
        return
    
    # تشغيل التطبيق
    success = run_streamlit_app()
    
    if not success:
        print("❌ فشل في تشغيل التطبيق")
        input("اضغط Enter للخروج...")

if __name__ == "__main__":
    main()
