# -*- coding: utf-8 -*-
"""
Language Manager - إدارة اللغات
نظام إدارة الترجمات للتطبيق
"""

import json
import streamlit as st
from pathlib import Path
from typing import Dict, Any

class LanguageManager:
    """مدير اللغات للتطبيق"""
    
    def __init__(self):
        self.translations_dir = Path("translations")
        self.available_languages = {
            "tr": {"name": "Türkçe", "flag": "🇹🇷", "direction": "ltr"},
            "ar": {"name": "العربية", "flag": "🇸🇦", "direction": "rtl"},
            "en": {"name": "English", "flag": "🇺🇸", "direction": "ltr"}
        }
        self.default_language = "tr"
        self.current_language = self.get_current_language()
        self.translations = self.load_translations()
    
    def get_current_language(self) -> str:
        """الحصول على اللغة الحالية"""
        if 'language' not in st.session_state:
            st.session_state.language = self.default_language
        return st.session_state.language
    
    def set_language(self, language_code: str):
        """تعيين اللغة الحالية"""
        if language_code in self.available_languages:
            st.session_state.language = language_code
            self.current_language = language_code
            self.translations = self.load_translations()
            st.rerun()
    
    def load_translations(self) -> Dict[str, Any]:
        """تحميل ترجمات اللغة الحالية"""
        try:
            file_path = self.translations_dir / f"{self.current_language}.json"
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # تحميل اللغة الافتراضية إذا لم توجد اللغة المطلوبة
                default_path = self.translations_dir / f"{self.default_language}.json"
                with open(default_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            st.error(f"خطأ في تحميل الترجمات: {e}")
            return {}
    
    def get_text(self, key: str, default: str = None) -> str:
        """الحصول على النص المترجم"""
        try:
            # البحث في المفاتيح المتداخلة
            keys = key.split('.')
            value = self.translations
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    # إذا لم يوجد المفتاح، استخدم القيمة الافتراضية أو المفتاح نفسه
                    return default if default else key
            
            return str(value) if value else (default if default else key)
            
        except Exception:
            return default if default else key
    
    def get_language_info(self, language_code: str = None) -> Dict[str, str]:
        """الحصول على معلومات اللغة"""
        lang_code = language_code or self.current_language
        return self.available_languages.get(lang_code, self.available_languages[self.default_language])
    
    def is_rtl(self) -> bool:
        """التحقق من اتجاه اللغة"""
        return self.get_language_info()["direction"] == "rtl"
    
    def get_language_selector(self) -> str:
        """عرض منتقي اللغة"""
        current_info = self.get_language_info()
        
        # إنشاء خيارات اللغة
        language_options = []
        for code, info in self.available_languages.items():
            language_options.append(f"{info['flag']} {info['name']}")
        
        # العثور على الفهرس الحالي
        current_index = list(self.available_languages.keys()).index(self.current_language)
        
        # عرض منتقي اللغة
        selected = st.selectbox(
            self.get_text("language", "Dil"),
            options=language_options,
            index=current_index,
            key="language_selector"
        )
        
        # الحصول على كود اللغة المختارة
        selected_index = language_options.index(selected)
        selected_code = list(self.available_languages.keys())[selected_index]
        
        # تغيير اللغة إذا تم اختيار لغة جديدة
        if selected_code != self.current_language:
            self.set_language(selected_code)
        
        return selected_code
    
    def apply_rtl_css(self):
        """تطبيق CSS للغات من اليمين إلى اليسار"""
        if self.is_rtl():
            st.markdown("""
            <style>
            .main .block-container {
                direction: rtl;
                text-align: right;
            }
            
            .stSelectbox > div > div {
                direction: rtl;
                text-align: right;
            }
            
            .stTextInput > div > div > input {
                direction: rtl;
                text-align: right;
            }
            
            .stTextArea > div > div > textarea {
                direction: rtl;
                text-align: right;
            }
            
            .stMarkdown {
                direction: rtl;
                text-align: right;
            }
            
            .stButton > button {
                direction: rtl;
            }
            
            /* Navigation menu RTL */
            .nav-link {
                direction: rtl;
                text-align: right;
            }
            
            /* Cards RTL */
            .metric-card, .violation-card, .card {
                direction: rtl;
                text-align: right;
            }
            </style>
            """, unsafe_allow_html=True)

# إنشاء مثيل عام لمدير اللغات
language_manager = LanguageManager()

def get_text(key: str, default: str = None) -> str:
    """دالة مساعدة للحصول على النص المترجم"""
    return language_manager.get_text(key, default)

def set_language(language_code: str):
    """دالة مساعدة لتعيين اللغة"""
    language_manager.set_language(language_code)

def get_current_language() -> str:
    """دالة مساعدة للحصول على اللغة الحالية"""
    return language_manager.current_language

def is_rtl() -> bool:
    """دالة مساعدة للتحقق من اتجاه اللغة"""
    return language_manager.is_rtl()

def apply_rtl_css():
    """دالة مساعدة لتطبيق CSS للغات RTL"""
    language_manager.apply_rtl_css()

def language_selector():
    """دالة مساعدة لعرض منتقي اللغة"""
    return language_manager.get_language_selector()
