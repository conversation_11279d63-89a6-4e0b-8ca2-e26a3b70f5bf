# -*- coding: utf-8 -*-
"""
Admin Dashboard - لوحة تحكم الأدمن
صفحة إدارة التطبيق للمدير
"""

import streamlit as st
from config import AppColors
from language_manager import get_text, apply_rtl_css
from utils import display_success_message, display_info_message, display_error_message
import json
from datetime import datetime, timed<PERSON><PERSON>

def show_admin_dashboard():
    """عرض لوحة تحكم الأدمن"""
    
    # تطبيق CSS للغات RTL
    apply_rtl_css()
    
    # التحقق من صلاحيات الأدمن
    if not hasattr(st.session_state.current_user, 'is_admin') or not st.session_state.current_user.is_admin:
        st.error("⛔ غير مصرح لك بالوصول لهذه الصفحة!")
        return
    
    # العنوان الرئيسي
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">
            👑 {get_text('admin_dashboard', 'Admin Dashboard')}
        </h1>
        <p style="color: #666;">مرحباً {st.session_state.current_user.full_name}</p>
    </div>
    """, unsafe_allow_html=True)
    
    # إحصائيات سريعة
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {AppColors.PRIMARY}, {AppColors.PRIMARY}CC);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 1rem;
        ">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">👥</div>
            <div style="font-size: 1.5rem; font-weight: bold;">1,247</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">إجمالي المستخدمين</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {AppColors.SUCCESS}, {AppColors.SUCCESS}CC);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 1rem;
        ">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📋</div>
            <div style="font-size: 1.5rem; font-weight: bold;">3,456</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">إجمالي البلاغات</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {AppColors.WARNING}, {AppColors.WARNING}CC);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 1rem;
        ">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">⏳</div>
            <div style="font-size: 1.5rem; font-weight: bold;">89</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">بلاغات معلقة</div>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div style="
            background: linear-gradient(135deg, {AppColors.INFO}, {AppColors.INFO}CC);
            color: white;
            padding: 1.5rem;
            border-radius: 12px;
            text-align: center;
            margin-bottom: 1rem;
        ">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
            <div style="font-size: 1.5rem; font-weight: bold;">₺45,678</div>
            <div style="font-size: 0.9rem; opacity: 0.9;">إجمالي المكافآت</div>
        </div>
        """, unsafe_allow_html=True)
    
    # تبويبات الإدارة
    tab1, tab2, tab3, tab4, tab5 = st.tabs([
        "📊 الإحصائيات", 
        "👥 المستخدمين", 
        "📋 البلاغات", 
        "⚙️ الإعدادات", 
        "📱 التطبيق"
    ])
    
    with tab1:
        show_statistics_tab()
    
    with tab2:
        show_users_tab()
    
    with tab3:
        show_reports_tab()
    
    with tab4:
        show_settings_tab()
    
    with tab5:
        show_app_management_tab()

def show_statistics_tab():
    """تبويب الإحصائيات"""
    st.markdown("### 📊 إحصائيات مفصلة")
    
    # رسوم بيانية وهمية
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        **📈 البلاغات الشهرية:**
        - يناير: 234 بلاغ
        - فبراير: 345 بلاغ  
        - مارس: 456 بلاغ
        - أبريل: 567 بلاغ
        """)
    
    with col2:
        st.markdown("""
        **🏆 أفضل المبلغين:**
        - أحمد محمد: 45 بلاغ
        - فاطمة علي: 38 بلاغ
        - محمد حسن: 32 بلاغ
        - سارة أحمد: 28 بلاغ
        """)

def show_users_tab():
    """تبويب المستخدمين"""
    st.markdown("### 👥 إدارة المستخدمين")
    
    # بحث المستخدمين
    search_user = st.text_input("🔍 البحث عن مستخدم", placeholder="البريد الإلكتروني أو الاسم")
    
    # قائمة المستخدمين الوهمية
    users_data = [
        {"name": "أحمد محمد", "email": "<EMAIL>", "reports": 45, "status": "نشط"},
        {"name": "فاطمة علي", "email": "<EMAIL>", "reports": 38, "status": "نشط"},
        {"name": "محمد حسن", "email": "<EMAIL>", "reports": 32, "status": "محظور"},
        {"name": "سارة أحمد", "email": "<EMAIL>", "reports": 28, "status": "نشط"},
    ]
    
    for user in users_data:
        with st.expander(f"👤 {user['name']} - {user['email']}"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"📊 البلاغات: {user['reports']}")
                st.write(f"📧 البريد: {user['email']}")
            
            with col2:
                status_color = AppColors.SUCCESS if user['status'] == 'نشط' else AppColors.ERROR
                st.markdown(f"**الحالة:** <span style='color: {status_color}'>{user['status']}</span>", unsafe_allow_html=True)
            
            with col3:
                if st.button(f"حظر المستخدم", key=f"ban_{user['email']}"):
                    display_success_message(f"تم حظر المستخدم {user['name']}")

def show_reports_tab():
    """تبويب البلاغات"""
    st.markdown("### 📋 إدارة البلاغات")
    
    # فلترة البلاغات
    col1, col2, col3 = st.columns(3)
    
    with col1:
        status_filter = st.selectbox("حالة البلاغ", ["الكل", "معلق", "مراجعة", "مقبول", "مرفوض"])
    
    with col2:
        type_filter = st.selectbox("نوع المخالفة", ["الكل", "مرور", "نظافة", "أمان", "وقوف"])
    
    with col3:
        date_filter = st.date_input("التاريخ")
    
    # قائمة البلاغات الوهمية
    reports_data = [
        {"id": "R001", "type": "مرور", "user": "أحمد محمد", "status": "معلق", "date": "2024-01-15"},
        {"id": "R002", "type": "نظافة", "user": "فاطمة علي", "status": "مقبول", "date": "2024-01-14"},
        {"id": "R003", "type": "وقوف", "user": "محمد حسن", "status": "مراجعة", "date": "2024-01-13"},
    ]
    
    for report in reports_data:
        with st.expander(f"📋 {report['id']} - {report['type']} - {report['user']}"):
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.write(f"**المبلغ:** {report['user']}")
                st.write(f"**النوع:** {report['type']}")
                st.write(f"**التاريخ:** {report['date']}")
            
            with col2:
                new_status = st.selectbox("تغيير الحالة", ["معلق", "مراجعة", "مقبول", "مرفوض"], 
                                        key=f"status_{report['id']}")
            
            with col3:
                if st.button("حفظ التغييرات", key=f"save_{report['id']}"):
                    display_success_message(f"تم تحديث حالة البلاغ {report['id']}")

def show_settings_tab():
    """تبويب الإعدادات"""
    st.markdown("### ⚙️ إعدادات النظام")
    
    # إعدادات عامة
    st.markdown("#### 🔧 الإعدادات العامة")
    
    col1, col2 = st.columns(2)
    
    with col1:
        maintenance_mode = st.checkbox("وضع الصيانة", help="تعطيل التطبيق مؤقتاً")
        auto_approve = st.checkbox("الموافقة التلقائية", help="موافقة تلقائية على البلاغات")
        email_notifications = st.checkbox("إشعارات البريد الإلكتروني", value=True)
    
    with col2:
        max_reports_per_day = st.number_input("الحد الأقصى للبلاغات يومياً", min_value=1, max_value=100, value=10)
        reward_percentage = st.slider("نسبة المكافأة (%)", min_value=5, max_value=50, value=20)
        min_withdrawal = st.number_input("الحد الأدنى للسحب (₺)", min_value=10, max_value=1000, value=50)
    
    if st.button("💾 حفظ الإعدادات", type="primary"):
        display_success_message("تم حفظ الإعدادات بنجاح!")

def show_app_management_tab():
    """تبويب إدارة التطبيق"""
    st.markdown("### 📱 إدارة التطبيق")
    
    # معلومات التطبيق
    st.markdown("#### ℹ️ معلومات التطبيق")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.info(f"""
        **اسم التطبيق:** مرشد
        **الإصدار:** 1.0.0
        **تاريخ الإطلاق:** 2024-01-01
        **المطور:** Mürşid Team
        """)
    
    with col2:
        st.info(f"""
        **الخادم:** متصل
        **قاعدة البيانات:** متصلة
        **التخزين:** 85% مستخدم
        **الذاكرة:** 2.1GB / 4GB
        """)
    
    # أدوات الإدارة
    st.markdown("#### 🛠️ أدوات الإدارة")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🔄 إعادة تشغيل النظام", type="secondary"):
            display_info_message("سيتم إعادة تشغيل النظام خلال 30 ثانية...")
    
    with col2:
        if st.button("🗑️ تنظيف الملفات المؤقتة", type="secondary"):
            display_success_message("تم تنظيف الملفات المؤقتة!")
    
    with col3:
        if st.button("📊 تصدير البيانات", type="secondary"):
            display_info_message("جاري تحضير ملف البيانات...")
    
    # سجل النشاطات
    st.markdown("#### 📜 سجل النشاطات الأخيرة")
    
    activities = [
        "2024-01-15 14:30 - تم قبول البلاغ R001",
        "2024-01-15 14:25 - مستخدم جديد: <EMAIL>",
        "2024-01-15 14:20 - تم رفض البلاغ R002",
        "2024-01-15 14:15 - تحديث إعدادات النظام",
        "2024-01-15 14:10 - تسجيل دخول المدير"
    ]
    
    for activity in activities:
        st.text(f"• {activity}")

if __name__ == "__main__":
    show_admin_dashboard()
