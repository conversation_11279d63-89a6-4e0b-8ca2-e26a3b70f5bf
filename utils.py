# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Utility Functions
"""

import streamlit as st
import json
import os
import hashlib
import base64
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any, List, Optional, Tuple
from PIL import Image
import io

from models import User, Report, Transaction, create_demo_user, create_demo_reports, create_demo_transactions
from config import ViolationType, ReportStatus, VIOLATION_CONFIG, STATUS_CONFIG, AppColors

# Session State Management
def init_session_state():
    """Initialize session state variables"""
    if 'authenticated' not in st.session_state:
        st.session_state.authenticated = False

    if 'current_user' not in st.session_state:
        st.session_state.current_user = None

    if 'current_page' not in st.session_state:
        st.session_state.current_page = 'login'

    if 'reports' not in st.session_state:
        st.session_state.reports = create_demo_reports()

    if 'transactions' not in st.session_state:
        st.session_state.transactions = create_demo_transactions()

    if 'notifications' not in st.session_state:
        st.session_state.notifications = []

# Authentication Functions
def hash_password(password: str) -> str:
    """Hash password using SHA256"""
    return hashlib.sha256(password.encode()).hexdigest()

def verify_password(password: str, hashed: str) -> bool:
    """Verify password against hash"""
    return hash_password(password) == hashed

def authenticate_user(email: str, password: str) -> Optional[User]:
    """Authenticate user using database"""
    from database import get_db_manager

    db = get_db_manager()
    user_data = db.authenticate_user(email, password)

    if user_data:
        # إنشاء كائن User من بيانات قاعدة البيانات
        user = User(
            id=str(user_data['id']),
            email=user_data['email'],
            full_name=user_data['full_name'],
            phone=user_data['phone'] or "",
            is_verified=user_data['is_verified']
        )
        # إضافة خاصية الأدمن
        user.is_admin = user_data['is_admin']
        return user

    return None

def register_user(email: str, password: str, full_name: str, phone: str = None) -> Tuple[bool, str]:
    """Register new user - returns (success, message)"""
    try:
        from database import get_db_manager

        db = get_db_manager()
        result = db.create_user(email, password, full_name, phone)

        if result:
            return True, "Kayıt başarılı!"
        else:
            return False, "Bu e-posta adresi zaten kullanılıyor!"

    except Exception as e:
        return False, f"Kayıt sırasında hata oluştu: {str(e)}"

def login_user(user: User):
    """Login user and set session state"""
    st.session_state.authenticated = True
    st.session_state.current_user = user
    st.session_state.current_page = 'home'

def logout_user():
    """Logout user and clear session state"""
    st.session_state.authenticated = False
    st.session_state.current_user = None
    st.session_state.current_page = 'login'

# Data Management Functions
def save_report(report: Report):
    """Save report to session state"""
    if 'reports' not in st.session_state:
        st.session_state.reports = []

    st.session_state.reports.append(report)

def get_user_reports(user_id: str) -> List[Report]:
    """Get reports for a specific user"""
    if 'reports' not in st.session_state:
        return []

    return [report for report in st.session_state.reports if report.user_id == user_id]

def get_user_transactions(user_id: str) -> List[Transaction]:
    """Get transactions for a specific user"""
    if 'transactions' not in st.session_state:
        return []

    return [txn for txn in st.session_state.transactions if txn.user_id == user_id]

def calculate_user_stats(user_id: str) -> Dict[str, Any]:
    """Calculate user statistics"""
    reports = get_user_reports(user_id)

    total_reports = len(reports)
    approved_reports = len([r for r in reports if r.status == ReportStatus.APPROVED])
    pending_reports = len([r for r in reports if r.status == ReportStatus.PENDING])
    rejected_reports = len([r for r in reports if r.status == ReportStatus.REJECTED])

    success_rate = (approved_reports / total_reports * 100) if total_reports > 0 else 0

    total_earnings = sum([r.reward_amount for r in reports if r.reward_amount])

    return {
        'total_reports': total_reports,
        'approved_reports': approved_reports,
        'pending_reports': pending_reports,
        'rejected_reports': rejected_reports,
        'success_rate': round(success_rate, 1),
        'total_earnings': total_earnings
    }

# UI Helper Functions
def display_violation_card(violation_type: ViolationType, selected: bool = False) -> bool:
    """Display violation type card and return if clicked"""
    config = VIOLATION_CONFIG[violation_type]

    card_class = "violation-card selected" if selected else "violation-card"

    with st.container():
        st.markdown(f"""
        <div class="{card_class}" style="border-color: {config['color'] if selected else 'transparent'};">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">{config['icon']}</div>
            <div style="font-weight: 500; color: {config['color']};">{config['name_tr']}</div>
            <div style="font-size: 0.875rem; color: #666; margin-top: 0.25rem;">{config['description']}</div>
        </div>
        """, unsafe_allow_html=True)

def display_status_badge(status: ReportStatus):
    """Display status badge"""
    config = STATUS_CONFIG[status]

    st.markdown(f"""
    <span class="status-badge status-{status.value}" style="background-color: {config['color']};">
        {config['icon']} {config['name_tr']}
    </span>
    """, unsafe_allow_html=True)

def display_metric_card(title: str, value: str, icon: str, color: str = AppColors.PRIMARY):
    """Display metric card"""
    st.markdown(f"""
    <div class="metric-card" style="background: linear-gradient(135deg, {color}, {color}CC);">
        <div style="font-size: 2rem; margin-bottom: 0.5rem;">{icon}</div>
        <div class="metric-value">{value}</div>
        <div class="metric-label">{title}</div>
    </div>
    """, unsafe_allow_html=True)

def display_report_card(report: Report):
    """Display report card"""
    config = VIOLATION_CONFIG[report.violation_type]
    status_config = STATUS_CONFIG[report.status]

    # Format date
    date_str = report.created_at.strftime("%d.%m.%Y %H:%M")

    st.markdown(f"""
    <div class="card">
        <div style="display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 1rem;">
            <div style="display: flex; align-items: center; gap: 0.5rem;">
                <span style="font-size: 1.5rem;">{config['icon']}</span>
                <div>
                    <div style="font-weight: 500; color: {config['color']};">{config['name_tr']}</div>
                    <div style="font-size: 0.875rem; color: #666;">#{report.id}</div>
                </div>
            </div>
            <span class="status-badge" style="background-color: {status_config['color']};">
                {status_config['icon']} {status_config['name_tr']}
            </span>
        </div>

        <div style="margin-bottom: 1rem;">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">Açıklama:</div>
            <div style="color: #666;">{report.description}</div>
        </div>

        <div style="margin-bottom: 1rem;">
            <div style="font-weight: 500; margin-bottom: 0.5rem;">📍 Konum:</div>
            <div style="color: #666;">{report.location.address}</div>
        </div>

        <div style="display: flex; justify-content: space-between; align-items: center; font-size: 0.875rem; color: #666;">
            <span>📅 {date_str}</span>
            {f'<span style="color: {AppColors.SUCCESS}; font-weight: 500;">💰 ₺{report.reward_amount:.2f}</span>' if report.reward_amount else ''}
        </div>

        {f'<div style="margin-top: 1rem; padding: 0.75rem; background-color: #f5f5f5; border-radius: 6px; font-size: 0.875rem;"><strong>Memur Notu:</strong> {report.officer_notes}</div>' if report.officer_notes else ''}
    </div>
    """, unsafe_allow_html=True)

def display_success_message(message: str):
    """Display success message"""
    st.markdown(f"""
    <div class="success-message">
        ✅ {message}
    </div>
    """, unsafe_allow_html=True)

def display_error_message(message: str):
    """Display error message"""
    st.markdown(f"""
    <div class="error-message">
        ❌ {message}
    </div>
    """, unsafe_allow_html=True)

def display_info_message(message: str):
    """Display info message"""
    st.markdown(f"""
    <div style="background-color: {AppColors.INFO}20; color: {AppColors.INFO}; padding: 1rem; border-radius: 8px; border-left: 4px solid {AppColors.INFO}; margin: 1rem 0;">
        ℹ️ {message}
    </div>
    """, unsafe_allow_html=True)

# Image Processing Functions
def process_uploaded_image(uploaded_file) -> Optional[str]:
    """Process uploaded image and return base64 string"""
    if uploaded_file is not None:
        try:
            # Read image
            image = Image.open(uploaded_file)

            # Resize if too large
            max_size = (800, 600)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # Convert to RGB if necessary
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # Save to bytes
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='JPEG', quality=85)
            img_bytes = img_buffer.getvalue()

            # Convert to base64
            img_base64 = base64.b64encode(img_bytes).decode()

            return f"data:image/jpeg;base64,{img_base64}"

        except Exception as e:
            st.error(f"Resim işlenirken hata oluştu: {str(e)}")
            return None

    return None

def display_image_from_base64(base64_string: str, caption: str = "", width: int = 300):
    """Display image from base64 string"""
    if base64_string:
        st.markdown(f"""
        <div style="text-align: center; margin: 1rem 0;">
            <img src="{base64_string}" style="max-width: {width}px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1);" />
            {f'<div style="margin-top: 0.5rem; font-size: 0.875rem; color: #666;">{caption}</div>' if caption else ''}
        </div>
        """, unsafe_allow_html=True)

# Location Functions
def get_demo_location() -> Dict[str, Any]:
    """Get demo location (Istanbul)"""
    return {
        "latitude": 41.0082,
        "longitude": 28.9784,
        "address": "İstanbul, Türkiye"
    }

def format_currency(amount: float) -> str:
    """Format currency amount"""
    return f"₺{amount:,.2f}"

def format_date(date: datetime) -> str:
    """Format date for display"""
    return date.strftime("%d.%m.%Y")

def format_datetime(date: datetime) -> str:
    """Format datetime for display"""
    return date.strftime("%d.%m.%Y %H:%M")

# Navigation Functions
def navigate_to(page: str):
    """Navigate to a specific page"""
    st.session_state.current_page = page
    st.rerun()

def get_current_page() -> str:
    """Get current page from session state"""
    return st.session_state.get('current_page', 'login')

# Validation Functions
def validate_email(email: str) -> bool:
    """Validate email format"""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """Validate phone format"""
    import re
    # Turkish phone number format
    pattern = r'^(\+90|0)?[5][0-9]{9}$'
    return re.match(pattern, phone.replace(' ', '').replace('-', '')) is not None
