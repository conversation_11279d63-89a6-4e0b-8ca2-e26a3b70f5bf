# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
New Report Page
"""

import streamlit as st
from datetime import datetime
from utils import (
    save_report, navigate_to, process_uploaded_image, display_image_from_base64,
    display_success_message, display_error_message, get_demo_location
)
from models import Report, Location, generate_report_id
from config import ViolationType, VIOLATION_CONFIG, AppColors, REWARD_PERCENTAGE

def show_new_report_page():
    """Display new report creation page"""
    
    if not st.session_state.authenticated:
        navigate_to('login')
        return
    
    user = st.session_state.current_user
    
    # Header
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">📝 Yeni İhbar</h1>
        <p style="color: #666; font-size: 1.1rem;"><PERSON><PERSON><PERSON> detaylı bir şekilde bildirin</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Back button
    if st.button("⬅️ Geri <PERSON>", help="Ana sayfaya dön"):
        navigate_to('home')
    
    st.markdown("---")
    
    # Report form
    with st.form("new_report_form", clear_on_submit=False):
        
        # Media upload section
        st.markdown("### 📸 Fotoğraf veya Video Ekle")
        st.markdown("*İhbarınızın onaylanması için kanıt fotoğrafı/videosu gereklidir*")
        
        uploaded_files = st.file_uploader(
            "Fotoğraf veya video seçin",
            type=['jpg', 'jpeg', 'png', 'mp4', 'mov'],
            accept_multiple_files=True,
            help="En fazla 5 dosya yükleyebilirsiniz"
        )
        
        # Display uploaded images
        if uploaded_files:
            st.markdown("**Yüklenen dosyalar:**")
            cols = st.columns(min(len(uploaded_files), 3))
            for i, uploaded_file in enumerate(uploaded_files[:3]):  # Show max 3 previews
                with cols[i % 3]:
                    if uploaded_file.type.startswith('image'):
                        st.image(uploaded_file, caption=uploaded_file.name, width=150)
                    else:
                        st.write(f"🎥 {uploaded_file.name}")
        
        st.markdown("---")
        
        # Violation type selection
        st.markdown("### 🏷️ İhlal Türü")
        st.markdown("*İhbar etmek istediğiniz ihlal türünü seçin*")
        
        # Check if violation type is pre-selected from home page
        preselected_violation = st.session_state.get('selected_violation_type', None)
        
        violation_options = []
        violation_values = []
        
        for violation_type in ViolationType:
            config = VIOLATION_CONFIG[violation_type]
            violation_options.append(f"{config['icon']} {config['name_tr']}")
            violation_values.append(violation_type)
        
        # Find index of preselected violation
        default_index = 0
        if preselected_violation:
            try:
                default_index = violation_values.index(preselected_violation)
            except ValueError:
                default_index = 0
        
        selected_violation_index = st.selectbox(
            "İhlal türünü seçin:",
            range(len(violation_options)),
            format_func=lambda x: violation_options[x],
            index=default_index,
            help="İhbar etmek istediğiniz ihlal türünü seçin"
        )
        
        selected_violation = violation_values[selected_violation_index]
        selected_config = VIOLATION_CONFIG[selected_violation]
        
        # Show violation info
        st.markdown(f"""
        <div style="padding: 1rem; background-color: {selected_config['color']}20; border-radius: 8px; border-left: 4px solid {selected_config['color']}; margin: 1rem 0;">
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                <span style="font-size: 1.5rem;">{selected_config['icon']}</span>
                <strong style="color: {selected_config['color']};">{selected_config['name_tr']}</strong>
            </div>
            <p style="margin: 0; color: #666;">{selected_config['description']}</p>
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("---")
        
        # Description
        st.markdown("### 📝 Açıklama")
        st.markdown("*İhlali detaylı bir şekilde açıklayın*")
        
        description = st.text_area(
            "İhbar açıklaması:",
            placeholder="İhbar etmek istediğiniz durumu detaylı olarak açıklayın...\n\nÖrnek: Kırmızı ışıkta geçen araç tespit edildi. Plaka: 34 ABC 123",
            height=120,
            help="Ne gördüğünüzü, ne zaman olduğunu ve varsa plaka bilgilerini yazın"
        )
        
        st.markdown("---")
        
        # Location section
        st.markdown("### 📍 Konum")
        st.markdown("*İhbar konumu otomatik olarak tespit edilecek*")
        
        # Demo location info
        demo_location = get_demo_location()
        
        st.markdown(f"""
        <div class="card">
            <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
                <span style="font-size: 1.5rem;">📍</span>
                <strong>Mevcut Konum</strong>
            </div>
            <p style="color: #666; margin: 0;">{demo_location['address']}</p>
            <p style="color: #999; font-size: 0.875rem; margin: 0.5rem 0 0 0;">
                Koordinatlar: {demo_location['latitude']:.4f}, {demo_location['longitude']:.4f}
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # Manual location option
        use_manual_location = st.checkbox("📝 Manuel konum gir", help="Farklı bir konum belirtmek istiyorsanız işaretleyin")
        
        manual_address = ""
        if use_manual_location:
            manual_address = st.text_input(
                "Konum adresi:",
                placeholder="Örnek: Sultanahmet Meydanı, Fatih, İstanbul",
                help="İhbarın gerçekleştiği adresi yazın"
            )
        
        st.markdown("---")
        
        # Submit button
        col1, col2 = st.columns([1, 2])
        
        with col1:
            cancel_button = st.form_submit_button(
                "❌ İptal",
                use_container_width=True
            )
        
        with col2:
            submit_button = st.form_submit_button(
                "🚀 İhbarı Gönder",
                use_container_width=True,
                type="primary"
            )
        
        # Form submission handling
        if cancel_button:
            # Clear any preselected violation type
            if 'selected_violation_type' in st.session_state:
                del st.session_state.selected_violation_type
            navigate_to('home')
        
        if submit_button:
            # Validation
            errors = []
            
            if not uploaded_files:
                errors.append("Lütfen en az bir fotoğraf veya video ekleyin!")
            
            if not description.strip():
                errors.append("Lütfen ihbar açıklamasını yazın!")
            
            if len(description.strip()) < 10:
                errors.append("Açıklama en az 10 karakter olmalıdır!")
            
            if use_manual_location and not manual_address.strip():
                errors.append("Manuel konum seçtiyseniz adresi yazın!")
            
            if errors:
                for error in errors:
                    display_error_message(error)
            else:
                # Process uploaded files
                media_urls = []
                for uploaded_file in uploaded_files:
                    if uploaded_file.type.startswith('image'):
                        processed_image = process_uploaded_image(uploaded_file)
                        if processed_image:
                            media_urls.append(processed_image)
                
                # Create location
                if use_manual_location and manual_address.strip():
                    location = Location(
                        latitude=demo_location['latitude'],  # Use demo coordinates
                        longitude=demo_location['longitude'],
                        address=manual_address.strip()
                    )
                else:
                    location = Location(
                        latitude=demo_location['latitude'],
                        longitude=demo_location['longitude'],
                        address=demo_location['address']
                    )
                
                # Create report
                new_report = Report(
                    id=generate_report_id(),
                    user_id=user.id,
                    violation_type=selected_violation,
                    description=description.strip(),
                    media_urls=media_urls,
                    location=location,
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )
                
                # Save report
                save_report(new_report)
                
                # Clear preselected violation type
                if 'selected_violation_type' in st.session_state:
                    del st.session_state.selected_violation_type
                
                # Show success message
                display_success_message(f"""
                İhbarınız başarıyla gönderildi! 
                
                **İhbar No:** {new_report.id}
                
                İhbarınız 24-48 saat içinde değerlendirilecek ve sonuç size bildirilecektir.
                Onaylanması durumunda %{int(REWARD_PERCENTAGE * 100)} ödül kazanacaksınız!
                """)
                
                # Auto redirect after 3 seconds
                st.balloons()
                
                if st.button("🏠 Ana Sayfaya Dön", use_container_width=True):
                    navigate_to('home')
    
    # Information section
    st.markdown("---")
    st.markdown("### ℹ️ Önemli Bilgiler")
    
    info_items = [
        ("📸", "Kaliteli Kanıt", "Net ve açık fotoğraflar çekin, bulanık görüntüler reddedilebilir"),
        ("⏰", "Hızlı Değerlendirme", "İhbarlarınız 24-48 saat içinde değerlendirilir"),
        ("💰", "Ödül Sistemi", f"Onaylanan ihbarlardan %{int(REWARD_PERCENTAGE * 100)} ödül alırsınız"),
        ("🔒", "Gizlilik", "Kişisel bilgileriniz gizli tutulur ve paylaşılmaz"),
        ("📱", "Takip", "İhbar durumunuzu 'İhbarlarım' sayfasından takip edebilirsiniz"),
        ("⚖️", "Yasal", "Sahte ihbar suçtur, doğru bilgiler verin")
    ]
    
    for i in range(0, len(info_items), 2):
        col_info1, col_info2 = st.columns(2)
        
        with col_info1:
            if i < len(info_items):
                icon, title, desc = info_items[i]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">{icon}</span>
                        <strong style="color: {AppColors.PRIMARY};">{title}</strong>
                    </div>
                    <div style="font-size: 0.875rem; color: #666;">{desc}</div>
                </div>
                """, unsafe_allow_html=True)
        
        with col_info2:
            if i + 1 < len(info_items):
                icon, title, desc = info_items[i + 1]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">{icon}</span>
                        <strong style="color: {AppColors.PRIMARY};">{title}</strong>
                    </div>
                    <div style="font-size: 0.875rem; color: #666;">{desc}</div>
                </div>
                """, unsafe_allow_html=True)
