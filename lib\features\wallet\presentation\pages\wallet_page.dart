import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';

class WalletPage extends ConsumerWidget {
  const WalletPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Mock data
    const totalEarnings = 1250.0;
    const availableBalance = 850.0;
    const pendingAmount = 400.0;

    return Scaffold(
      appBar: AppBar(
        title: Text('wallet'.tr()),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Balance Cards
            Row(
              children: [
                Expanded(
                  child: _buildBalanceCard(
                    context,
                    'total_earnings'.tr(),
                    '₺${totalEarnings.toStringAsFixed(2)}',
                    Icons.trending_up,
                    AppColors.secondary,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildBalanceCard(
                    context,
                    'available_balance'.tr(),
                    '₺${availableBalance.toStringAsFixed(2)}',
                    Icons.account_balance_wallet,
                    AppColors.primary,
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Pending Amount Card
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: AppColors.pending.withOpacity(0.1),
                        shape: BoxShape.circle,
                      ),
                      child: Icon(
                        Icons.hourglass_empty,
                        color: AppColors.pending,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Bekleyen Kazanç',
                            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                              color: Colors.grey[600],
                            ),
                          ),
                          Text(
                            '₺${pendingAmount.toStringAsFixed(2)}',
                            style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.pending,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Withdraw Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: availableBalance >= 50 ? () {
                  _showWithdrawDialog(context, availableBalance);
                } : null,
                icon: const Icon(Icons.money),
                label: Text('withdraw_money'.tr()),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: AppColors.secondary,
                  foregroundColor: Colors.white,
                ),
              ),
            ),
            
            if (availableBalance < 50) ...[
              const SizedBox(height: 8),
              Text(
                'minimum_withdrawal'.tr() + ': ₺50',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Transaction History
            Text(
              'İşlem Geçmişi',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Mock transaction history
            _buildTransactionItem(
              context,
              'Trafik İhlali Ödülü',
              '+₺200.00',
              DateTime.now().subtract(const Duration(days: 1)),
              true,
            ),
            _buildTransactionItem(
              context,
              'Para Çekme',
              '-₺150.00',
              DateTime.now().subtract(const Duration(days: 3)),
              false,
            ),
            _buildTransactionItem(
              context,
              'Temizlik İhlali Ödülü',
              '+₺100.00',
              DateTime.now().subtract(const Duration(days: 5)),
              true,
            ),
            _buildTransactionItem(
              context,
              'Park İhlali Ödülü',
              '+₺75.00',
              DateTime.now().subtract(const Duration(days: 7)),
              true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBalanceCard(
    BuildContext context,
    String title,
    String amount,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              amount,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTransactionItem(
    BuildContext context,
    String title,
    String amount,
    DateTime date,
    bool isIncome,
  ) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: (isIncome ? AppColors.secondary : AppColors.error).withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            isIncome ? Icons.add : Icons.remove,
            color: isIncome ? AppColors.secondary : AppColors.error,
            size: 20,
          ),
        ),
        title: Text(title),
        subtitle: Text(DateFormat('dd/MM/yyyy HH:mm').format(date)),
        trailing: Text(
          amount,
          style: Theme.of(context).textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: isIncome ? AppColors.secondary : AppColors.error,
          ),
        ),
      ),
    );
  }

  void _showWithdrawDialog(BuildContext context, double availableBalance) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('withdraw_money'.tr()),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Kullanılabilir Bakiye: ₺${availableBalance.toStringAsFixed(2)}'),
            const SizedBox(height: 16),
            TextFormField(
              keyboardType: TextInputType.number,
              decoration: const InputDecoration(
                labelText: 'Çekilecek Tutar',
                prefixText: '₺',
                border: OutlineInputBorder(),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'withdrawal_fee'.tr() + ': ₺5.00',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('cancel'.tr()),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Para çekme talebiniz işleme alındı'),
                ),
              );
            },
            child: Text('withdraw'.tr()),
          ),
        ],
      ),
    );
  }
}
