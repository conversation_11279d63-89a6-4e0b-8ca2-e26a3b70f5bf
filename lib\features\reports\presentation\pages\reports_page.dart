import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../models/report.dart';

class ReportsPage extends ConsumerWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Mock data for demonstration
    final reports = [
      Report(
        id: '1',
        userId: 'user1',
        violationType: ViolationType.traffic,
        description: 'Kırmızı ışıkta geçen araç',
        mediaUrls: ['image1.jpg'],
        location: const Location(
          latitude: 41.0082,
          longitude: 28.9784,
          address: 'Sultanahmet, İstanbul',
        ),
        status: ReportStatus.approved,
        fineAmount: 1000,
        rewardAmount: 200,
        createdAt: DateTime.now().subtract(const Duration(days: 2)),
        updatedAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      Report(
        id: '2',
        userId: 'user1',
        violationType: ViolationType.cleanliness,
        description: 'Çö<PERSON> atma ihlali',
        mediaUrls: ['image2.jpg'],
        location: const Location(
          latitude: 41.0082,
          longitude: 28.9784,
          address: 'Beşiktaş, İstanbul',
        ),
        status: ReportStatus.inReview,
        createdAt: DateTime.now().subtract(const Duration(hours: 5)),
        updatedAt: DateTime.now().subtract(const Duration(hours: 5)),
      ),
      Report(
        id: '3',
        userId: 'user1',
        violationType: ViolationType.parking,
        description: 'Engelli park yerine park etme',
        mediaUrls: ['image3.jpg'],
        location: const Location(
          latitude: 41.0082,
          longitude: 28.9784,
          address: 'Kadıköy, İstanbul',
        ),
        status: ReportStatus.rejected,
        officerNotes: 'Yeterli kanıt bulunamadı',
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
        updatedAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];

    return Scaffold(
      appBar: AppBar(
        title: Text('my_reports'.tr()),
      ),
      body: ListView.builder(
        padding: const EdgeInsets.all(16.0),
        itemCount: reports.length,
        itemBuilder: (context, index) {
          final report = reports[index];
          return _buildReportCard(context, report);
        },
      ),
    );
  }

  Widget _buildReportCard(BuildContext context, Report report) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with violation type and status
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: _getViolationColor(report.violationType).withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        _getViolationIcon(report.violationType),
                        color: _getViolationColor(report.violationType),
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'violation_types.${report.violationType.name}'.tr(),
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          DateFormat('dd/MM/yyyy HH:mm').format(report.createdAt),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
                _buildStatusChip(report.status),
              ],
            ),
            
            const SizedBox(height: 12),
            
            // Description
            Text(
              report.description,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            
            const SizedBox(height: 8),
            
            // Location
            Row(
              children: [
                Icon(Icons.location_on, size: 16, color: Colors.grey[600]),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    report.location.address,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              ],
            ),
            
            // Reward info (if approved)
            if (report.status == ReportStatus.approved && report.rewardAmount != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.secondary.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.account_balance_wallet, color: AppColors.secondary),
                    const SizedBox(width: 8),
                    Text(
                      'Kazanç: ₺${report.rewardAmount!.toStringAsFixed(2)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppColors.secondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            
            // Officer notes (if rejected)
            if (report.status == ReportStatus.rejected && report.officerNotes != null) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(Icons.info_outline, color: AppColors.error, size: 20),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        report.officerNotes!,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppColors.error,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusChip(ReportStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case ReportStatus.pending:
        color = AppColors.pending;
        text = 'report_status.pending'.tr();
        break;
      case ReportStatus.inReview:
        color = AppColors.inReview;
        text = 'report_status.in_review'.tr();
        break;
      case ReportStatus.approved:
        color = AppColors.approved;
        text = 'report_status.approved'.tr();
        break;
      case ReportStatus.rejected:
        color = AppColors.rejected;
        text = 'report_status.rejected'.tr();
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Text(
        text,
        style: TextStyle(
          color: color,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Color _getViolationColor(ViolationType type) {
    switch (type) {
      case ViolationType.traffic:
        return AppColors.trafficViolation;
      case ViolationType.cleanliness:
        return AppColors.cleanlinessViolation;
      case ViolationType.harassment:
        return AppColors.harassmentViolation;
      case ViolationType.violence:
        return AppColors.violenceViolation;
      case ViolationType.noise:
        return AppColors.noiseViolation;
      case ViolationType.parking:
        return AppColors.parkingViolation;
      case ViolationType.other:
        return AppColors.otherViolation;
    }
  }

  IconData _getViolationIcon(ViolationType type) {
    switch (type) {
      case ViolationType.traffic:
        return Icons.traffic;
      case ViolationType.cleanliness:
        return Icons.cleaning_services;
      case ViolationType.harassment:
        return Icons.warning;
      case ViolationType.violence:
        return Icons.dangerous;
      case ViolationType.noise:
        return Icons.volume_up;
      case ViolationType.parking:
        return Icons.local_parking;
      case ViolationType.other:
        return Icons.report_problem;
    }
  }
}
