@echo off
chcp 65001 >nul
echo ========================================
echo    🛡️ مرشد - تطبيق سطح المكتب
echo ========================================
echo.

echo [1/3] فحص Python...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ خطأ: Python غير مثبت!
    echo يرجى تثبيت Python 3.8+ من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python موجود

echo.
echo [2/3] تثبيت المتطلبات...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ خطأ: فشل في تثبيت المتطلبات!
    pause
    exit /b 1
)
echo ✅ تم تثبيت المتطلبات

echo.
echo [3/3] تشغيل التطبيق...
echo.
echo ========================================
echo  🚀 جاري تشغيل التطبيق...
echo  سيتم فتح نافذة التطبيق تلقائياً
echo ========================================
echo.

python desktop_app.py

echo.
echo تم إغلاق التطبيق.
pause
