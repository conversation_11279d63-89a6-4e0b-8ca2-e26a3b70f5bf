#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرشد - تطبيق نافذة GUI عادية
تطبيق نافذة منفصلة بدون متصفح
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
import hashlib
from datetime import datetime
import os

class MurshidApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_database()
        self.current_user = None
        self.setup_styles()
        self.show_login_page()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🛡️ مرشد - تطبيق الإبلاغ عن المخالفات")
        self.root.geometry("360x640")
        self.root.resizable(False, False)

        # توسيط النافذة
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (360 // 2)
        y = (self.root.winfo_screenheight() // 2) - (640 // 2)
        self.root.geometry(f"360x640+{x}+{y}")

        # إعداد الألوان
        self.colors = {
            'primary': '#1f77b4',
            'success': '#28a745',
            'error': '#dc3545',
            'warning': '#ffc107',
            'light': '#f8f9fa',
            'dark': '#343a40',
            'white': '#ffffff'
        }

        self.root.configure(bg=self.colors['light'])

    def setup_styles(self):
        """إعداد الأنماط"""
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # خط عربي
        self.arabic_font = font.Font(family="Arial", size=10)
        self.title_font = font.Font(family="Arial", size=14, weight="bold")
        self.header_font = font.Font(family="Arial", size=12, weight="bold")

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        # جدول المستخدمين
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        # إنشاء حساب الأدمن
        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
            VALUES (?, ?, ?, ?)
        ''', ("<EMAIL>", admin_password, "System Administrator", True))

        conn.commit()
        conn.close()

    def hash_password(self, password):
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate_user(self, email, password):
        """التحقق من المستخدم"""
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        hashed_password = self.hash_password(password)
        cursor.execute('''
            SELECT id, email, full_name, is_admin
            FROM users
            WHERE email = ? AND password = ?
        ''', (email, hashed_password))

        user = cursor.fetchone()
        conn.close()

        if user:
            return {
                'id': user[0],
                'email': user[1],
                'full_name': user[2],
                'is_admin': bool(user[3])
            }
        return None

    def register_user(self, email, password, full_name, phone=None):
        """تسجيل مستخدم جديد"""
        try:
            conn = sqlite3.connect('mursid.db')
            cursor = conn.cursor()

            hashed_password = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (email, password, full_name, phone)
                VALUES (?, ?, ?, ?)
            ''', (email, hashed_password, full_name, phone))

            conn.commit()
            conn.close()
            return True, "تم التسجيل بنجاح!"

        except sqlite3.IntegrityError:
            return False, "هذا البريد الإلكتروني مستخدم بالفعل!"
        except Exception as e:
            return False, f"خطأ في التسجيل: {str(e)}"

    def clear_window(self):
        """مسح محتوى النافذة"""
        for widget in self.root.winfo_children():
            widget.destroy()

    def show_login_page(self):
        """عرض صفحة تسجيل الدخول"""
        self.clear_window()

        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="🛡️ مرشد",
            font=self.title_font,
            fg=self.colors['primary'],
            bg=self.colors['light']
        )
        title_label.pack(pady=(0, 5))

        subtitle_label = tk.Label(
            main_frame,
            text="تطبيق الإبلاغ عن المخالفات",
            font=self.arabic_font,
            fg=self.colors['dark'],
            bg=self.colors['light']
        )
        subtitle_label.pack(pady=(0, 30))

        # إطار تسجيل الدخول
        login_frame = tk.Frame(main_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        login_frame.pack(fill=tk.X, pady=10)

        # عنوان تسجيل الدخول
        login_title = tk.Label(
            login_frame,
            text="🔐 تسجيل الدخول",
            font=self.header_font,
            fg=self.colors['dark'],
            bg=self.colors['white']
        )
        login_title.pack(pady=(15, 10))

        # حقل البريد الإلكتروني
        tk.Label(
            login_frame,
            text="📧 البريد الإلكتروني:",
            font=self.arabic_font,
            bg=self.colors['white']
        ).pack(anchor=tk.W, padx=15, pady=(10, 5))

        self.email_entry = tk.Entry(
            login_frame,
            font=self.arabic_font,
            width=30,
            relief=tk.SOLID,
            bd=1
        )
        self.email_entry.pack(padx=15, pady=(0, 10), ipady=5)
        self.email_entry.insert(0, "<EMAIL>")

        # حقل كلمة المرور
        tk.Label(
            login_frame,
            text="🔒 كلمة المرور:",
            font=self.arabic_font,
            bg=self.colors['white']
        ).pack(anchor=tk.W, padx=15, pady=(0, 5))

        self.password_entry = tk.Entry(
            login_frame,
            font=self.arabic_font,
            width=30,
            show="*",
            relief=tk.SOLID,
            bd=1
        )
        self.password_entry.pack(padx=15, pady=(0, 15), ipady=5)
        self.password_entry.insert(0, "admin123")

        # أزرار
        button_frame = tk.Frame(login_frame, bg=self.colors['white'])
        button_frame.pack(fill=tk.X, padx=15, pady=(0, 15))

        login_btn = tk.Button(
            button_frame,
            text="🚀 دخول",
            font=self.arabic_font,
            bg=self.colors['primary'],
            fg=self.colors['white'],
            relief=tk.FLAT,
            command=self.login,
            cursor="hand2"
        )
        login_btn.pack(fill=tk.X, pady=(0, 5), ipady=8)

        register_btn = tk.Button(
            button_frame,
            text="📝 تسجيل جديد",
            font=self.arabic_font,
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief=tk.FLAT,
            command=self.show_register_page,
            cursor="hand2"
        )
        register_btn.pack(fill=tk.X, ipady=8)

        # معلومات تجريبية
        info_frame = tk.Frame(main_frame, bg=self.colors['light'])
        info_frame.pack(fill=tk.X, pady=20)

        info_text = """معلومات تجريبية:

👑 حساب الأدمن:
البريد: <EMAIL>
كلمة المرور: admin123

📝 أو أنشئ حساب جديد"""

        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=self.arabic_font,
            bg=self.colors['light'],
            fg=self.colors['dark'],
            justify=tk.LEFT
        )
        info_label.pack()

    def login(self):
        """تسجيل الدخول"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        if not email or not password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول!")
            return

        user = self.authenticate_user(email, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
            self.show_main_page()
        else:
            messagebox.showerror("خطأ", "بيانات خاطئة!")

    def show_register_page(self):
        """عرض صفحة التسجيل"""
        self.clear_window()

        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان
        title_label = tk.Label(
            main_frame,
            text="📝 تسجيل حساب جديد",
            font=self.title_font,
            fg=self.colors['primary'],
            bg=self.colors['light']
        )
        title_label.pack(pady=(0, 20))

        # إطار التسجيل
        register_frame = tk.Frame(main_frame, bg=self.colors['white'], relief=tk.RAISED, bd=1)
        register_frame.pack(fill=tk.X, pady=10)

        # الحقول
        fields = [
            ("👤 الاسم الكامل:", "full_name"),
            ("📧 البريد الإلكتروني:", "email"),
            ("📱 رقم الهاتف:", "phone"),
            ("🔒 كلمة المرور:", "password"),
            ("🔒 تأكيد كلمة المرور:", "confirm_password")
        ]

        self.register_entries = {}

        for label_text, field_name in fields:
            tk.Label(
                register_frame,
                text=label_text,
                font=self.arabic_font,
                bg=self.colors['white']
            ).pack(anchor=tk.W, padx=15, pady=(10, 5))

            entry = tk.Entry(
                register_frame,
                font=self.arabic_font,
                width=30,
                relief=tk.SOLID,
                bd=1,
                show="*" if "password" in field_name else ""
            )
            entry.pack(padx=15, pady=(0, 5), ipady=5)
            self.register_entries[field_name] = entry

        # أزرار
        button_frame = tk.Frame(register_frame, bg=self.colors['white'])
        button_frame.pack(fill=tk.X, padx=15, pady=15)

        register_btn = tk.Button(
            button_frame,
            text="✅ تسجيل",
            font=self.arabic_font,
            bg=self.colors['success'],
            fg=self.colors['white'],
            relief=tk.FLAT,
            command=self.register,
            cursor="hand2"
        )
        register_btn.pack(fill=tk.X, pady=(0, 5), ipady=8)

        back_btn = tk.Button(
            button_frame,
            text="🔙 رجوع",
            font=self.arabic_font,
            bg=self.colors['warning'],
            fg=self.colors['white'],
            relief=tk.FLAT,
            command=self.show_login_page,
            cursor="hand2"
        )
        back_btn.pack(fill=tk.X, ipady=8)

    def register(self):
        """تسجيل مستخدم جديد"""
        # الحصول على البيانات
        full_name = self.register_entries['full_name'].get().strip()
        email = self.register_entries['email'].get().strip()
        phone = self.register_entries['phone'].get().strip()
        password = self.register_entries['password'].get().strip()
        confirm_password = self.register_entries['confirm_password'].get().strip()

        # التحقق من البيانات
        if not all([full_name, email, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة!")
            return

        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل!")
            return

        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمات المرور غير متطابقة!")
            return

        # محاولة التسجيل
        success, message = self.register_user(email, password, full_name, phone)

        if success:
            messagebox.showinfo("نجح", message)
            self.show_login_page()
        else:
            messagebox.showerror("خطأ", message)

    def show_main_page(self):
        """عرض الصفحة الرئيسية"""
        self.clear_window()

        # إطار رئيسي
        main_frame = tk.Frame(self.root, bg=self.colors['light'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # شريط علوي
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=60)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # معلومات المستخدم
        user_info = f"👋 مرحباً {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " 👑"

        user_label = tk.Label(
            header_frame,
            text=user_info,
            font=self.header_font,
            fg=self.colors['white'],
            bg=self.colors['primary']
        )
        user_label.pack(side=tk.LEFT, padx=15, pady=15)

        # زر تسجيل الخروج
        logout_btn = tk.Button(
            header_frame,
            text="🚪 خروج",
            font=self.arabic_font,
            bg=self.colors['error'],
            fg=self.colors['white'],
            relief=tk.FLAT,
            command=self.logout,
            cursor="hand2"
        )
        logout_btn.pack(side=tk.RIGHT, padx=15, pady=15)

        # منطقة المحتوى
        content_frame = tk.Frame(main_frame, bg=self.colors['light'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # القائمة الرئيسية
        menu_items = [
            ("🏠 الصفحة الرئيسية", self.show_home_content),
            ("📝 بلاغ جديد", self.show_new_report),
            ("📋 بلاغاتي", self.show_my_reports),
            ("💰 المحفظة", self.show_wallet),
            ("👤 الملف الشخصي", self.show_profile)
        ]

        # إضافة لوحة تحكم الأدمن
        if self.current_user['is_admin']:
            menu_items.insert(0, ("👑 لوحة التحكم", self.show_admin_dashboard))

        for text, command in menu_items:
            btn = tk.Button(
                content_frame,
                text=text,
                font=self.header_font,
                bg=self.colors['white'],
                fg=self.colors['dark'],
                relief=tk.RAISED,
                bd=1,
                command=command,
                cursor="hand2",
                anchor=tk.W,
                padx=20
            )
            btn.pack(fill=tk.X, pady=5, ipady=15)

    def show_home_content(self):
        """عرض محتوى الصفحة الرئيسية"""
        messagebox.showinfo("الصفحة الرئيسية", "🏠 مرحباً بك في تطبيق مرشد!\n\nيمكنك الإبلاغ عن المخالفات وكسب المكافآت.")

    def show_new_report(self):
        """عرض صفحة بلاغ جديد"""
        messagebox.showinfo("بلاغ جديد", "📝 هذه الميزة قيد التطوير\n\nسيتم إضافة نموذج الإبلاغ قريباً.")

    def show_my_reports(self):
        """عرض بلاغاتي"""
        messagebox.showinfo("بلاغاتي", "📋 هذه الميزة قيد التطوير\n\nسيتم عرض قائمة بلاغاتك هنا.")

    def show_wallet(self):
        """عرض المحفظة"""
        messagebox.showinfo("المحفظة", "💰 هذه الميزة قيد التطوير\n\nسيتم عرض رصيدك والمعاملات هنا.")

    def show_profile(self):
        """عرض الملف الشخصي"""
        profile_info = f"""👤 الملف الشخصي

الاسم: {self.current_user['full_name']}
البريد: {self.current_user['email']}
النوع: {'مدير النظام' if self.current_user['is_admin'] else 'مستخدم عادي'}

هذه الميزة قيد التطوير لإضافة المزيد من التفاصيل."""

        messagebox.showinfo("الملف الشخصي", profile_info)

    def show_admin_dashboard(self):
        """عرض لوحة تحكم الأدمن"""
        if not self.current_user['is_admin']:
            messagebox.showerror("خطأ", "غير مصرح لك بالوصول لهذه الصفحة!")
            return

        dashboard_info = """👑 لوحة تحكم المدير

📊 الإحصائيات:
👥 المستخدمين: 1,247
📋 البلاغات: 3,456
✅ المقبولة: 2,890
💰 المكافآت: ₺45,678

🛠️ الأدوات:
• إدارة المستخدمين
• مراجعة البلاغات
• إعدادات النظام
• التقارير والإحصائيات

هذه الميزة قيد التطوير لإضافة المزيد من الأدوات."""

        messagebox.showinfo("لوحة تحكم المدير", dashboard_info)

    def logout(self):
        """تسجيل الخروج"""
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.current_user = None
            self.show_login_page()

    def run(self):
        """تشغيل التطبيق"""
        self.root.mainloop()

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل تطبيق مرشد...")
    print("📱 نافذة GUI عادية - بدون متصفح")
    print("🔧 حجم النافذة: 360x640")

    app = MurshidApp()
    app.run()

if __name__ == "__main__":
    main()
