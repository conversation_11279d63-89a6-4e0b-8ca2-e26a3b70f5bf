import 'package:flutter/material.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:go_router/go_router.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../core/theme/app_colors.dart';
import '../../../../models/report.dart';

class NewReportPage extends HookConsumerWidget {
  const NewReportPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedViolationType = useState<ViolationType?>(null);
    final descriptionController = useTextEditingController();
    final selectedMedia = useState<List<XFile>>([]);
    final isLoading = useState(false);

    return Scaffold(
      appBar: AppBar(
        title: Text('new_report'.tr()),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () => context.pop(),
        ),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Media Section
            Text(
              'Fotoğraf veya Video Ekle',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Row(
              children: [
                Expanded(
                  child: _buildMediaButton(
                    context,
                    'take_photo'.tr(),
                    Icons.camera_alt,
                    () async {
                      final picker = ImagePicker();
                      final image = await picker.pickImage(source: ImageSource.camera);
                      if (image != null) {
                        selectedMedia.value = [...selectedMedia.value, image];
                      }
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildMediaButton(
                    context,
                    'take_video'.tr(),
                    Icons.videocam,
                    () async {
                      final picker = ImagePicker();
                      final video = await picker.pickVideo(source: ImageSource.camera);
                      if (video != null) {
                        selectedMedia.value = [...selectedMedia.value, video];
                      }
                    },
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            ElevatedButton.icon(
              onPressed: () async {
                final picker = ImagePicker();
                final media = await picker.pickMultipleMedia();
                selectedMedia.value = [...selectedMedia.value, ...media];
              },
              icon: const Icon(Icons.photo_library),
              label: Text('select_from_gallery'.tr()),
              style: ElevatedButton.styleFrom(
                minimumSize: const Size(double.infinity, 48),
              ),
            ),
            
            // Selected Media Preview
            if (selectedMedia.value.isNotEmpty) ...[
              const SizedBox(height: 16),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: selectedMedia.value.length,
                  itemBuilder: (context, index) {
                    return Container(
                      width: 100,
                      margin: const EdgeInsets.only(right: 8),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Stack(
                        children: [
                          ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: Container(
                              width: 100,
                              height: 100,
                              color: Colors.grey[200],
                              child: const Icon(Icons.image, size: 40),
                            ),
                          ),
                          Positioned(
                            top: 4,
                            right: 4,
                            child: GestureDetector(
                              onTap: () {
                                final newList = [...selectedMedia.value];
                                newList.removeAt(index);
                                selectedMedia.value = newList;
                              },
                              child: Container(
                                padding: const EdgeInsets.all(2),
                                decoration: const BoxDecoration(
                                  color: Colors.red,
                                  shape: BoxShape.circle,
                                ),
                                child: const Icon(
                                  Icons.close,
                                  size: 16,
                                  color: Colors.white,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ],
            
            const SizedBox(height: 24),
            
            // Violation Type Section
            Text(
              'violation_type'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: ViolationType.values.map((type) {
                final isSelected = selectedViolationType.value == type;
                return FilterChip(
                  label: Text('violation_types.${type.name}'.tr()),
                  selected: isSelected,
                  onSelected: (selected) {
                    selectedViolationType.value = selected ? type : null;
                  },
                  backgroundColor: isSelected ? _getViolationColor(type) : null,
                  selectedColor: _getViolationColor(type),
                );
              }).toList(),
            ),
            
            const SizedBox(height: 24),
            
            // Description Section
            Text(
              'description'.tr(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            
            TextFormField(
              controller: descriptionController,
              maxLines: 4,
              decoration: InputDecoration(
                hintText: 'İhbar etmek istediğiniz durumu detaylı olarak açıklayın...',
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Location Section
            Card(
              child: ListTile(
                leading: const Icon(Icons.location_on),
                title: Text('location'.tr()),
                subtitle: const Text('Mevcut konumunuz otomatik olarak eklenecek'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  // TODO: Open location picker
                },
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Submit Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: isLoading.value
                    ? null
                    : () async {
                        if (selectedMedia.value.isEmpty) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('please_add_media'.tr())),
                          );
                          return;
                        }
                        
                        if (selectedViolationType.value == null) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('please_select_violation_type'.tr())),
                          );
                          return;
                        }
                        
                        isLoading.value = true;
                        // TODO: Submit report
                        await Future.delayed(const Duration(seconds: 2));
                        isLoading.value = false;
                        
                        if (context.mounted) {
                          ScaffoldMessenger.of(context).showSnackBar(
                            SnackBar(content: Text('report_submitted_successfully'.tr())),
                          );
                          context.pop();
                        }
                      },
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child: isLoading.value
                    ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : Text('submit_report'.tr()),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMediaButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton.icon(
      onPressed: onPressed,
      icon: Icon(icon),
      label: Text(label),
      style: ElevatedButton.styleFrom(
        minimumSize: const Size(double.infinity, 48),
      ),
    );
  }

  Color _getViolationColor(ViolationType type) {
    switch (type) {
      case ViolationType.traffic:
        return AppColors.trafficViolation;
      case ViolationType.cleanliness:
        return AppColors.cleanlinessViolation;
      case ViolationType.harassment:
        return AppColors.harassmentViolation;
      case ViolationType.violence:
        return AppColors.violenceViolation;
      case ViolationType.noise:
        return AppColors.noiseViolation;
      case ViolationType.parking:
        return AppColors.parkingViolation;
      case ViolationType.other:
        return AppColors.otherViolation;
    }
  }
}
