# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Profile Page
"""

import streamlit as st
from utils import (
    logout_user, navigate_to, calculate_user_stats,
    display_success_message, display_error_message, validate_email, validate_phone
)
from config import AppColors, APP_NAME, APP_VERSION

def show_profile_page():
    """Display profile page"""
    
    if not st.session_state.authenticated:
        navigate_to('login')
        return
    
    user = st.session_state.current_user
    
    # Header
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">👤 Profil</h1>
        <p style="color: #666; font-size: 1.1rem;">Hesap bilgilerinizi yönetin</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Back button
    if st.button("⬅️ <PERSON> Say<PERSON>", help="Ana sayfaya dön"):
        navigate_to('home')
    
    # Profile header card
    st.markdown("### 👤 Profil Bilgileri")
    
    # Calculate user stats
    stats = calculate_user_stats(user.id)
    
    st.markdown(f"""
    <div class="card">
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="
                width: 100px; 
                height: 100px; 
                background-color: {AppColors.PRIMARY}; 
                border-radius: 50%; 
                display: flex; 
                align-items: center; 
                justify-content: center; 
                margin: 0 auto 1rem auto;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ">
                <span style="font-size: 3rem; color: white;">👤</span>
            </div>
            <h2 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">{user.full_name}</h2>
            <p style="color: #666; margin-bottom: 1rem;">{user.email}</p>
            
            <div style="display: flex; justify-content: space-around; margin-top: 1.5rem;">
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold; color: {AppColors.PRIMARY};">{stats['total_reports']}</div>
                    <div style="font-size: 0.875rem; color: #666;">İhbar</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold; color: {AppColors.SECONDARY};">₺{stats['total_earnings']:.0f}</div>
                    <div style="font-size: 0.875rem; color: #666;">Kazanç</div>
                </div>
                <div style="text-align: center;">
                    <div style="font-size: 1.5rem; font-weight: bold; color: {AppColors.SUCCESS};">%{stats['success_rate']}</div>
                    <div style="font-size: 0.875rem; color: #666;">Başarı</div>
                </div>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    st.markdown("---")
    
    # Profile edit section
    st.markdown("### ✏️ Profili Düzenle")
    
    with st.form("profile_edit_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            new_full_name = st.text_input(
                "👤 Ad Soyad:",
                value=user.full_name,
                help="Adınızı ve soyadınızı girin"
            )
            
            new_email = st.text_input(
                "📧 E-posta:",
                value=user.email,
                help="E-posta adresinizi girin"
            )
        
        with col2:
            new_phone = st.text_input(
                "📱 Telefon:",
                value=user.phone,
                help="Telefon numaranızı girin"
            )
            
            # Profile image upload (placeholder)
            profile_image = st.file_uploader(
                "📷 Profil Fotoğrafı:",
                type=['jpg', 'jpeg', 'png'],
                help="Profil fotoğrafınızı yükleyin"
            )
        
        # Password change section
        st.markdown("**🔒 Şifre Değiştir** (İsteğe bağlı)")
        
        col_pass1, col_pass2 = st.columns(2)
        
        with col_pass1:
            current_password = st.text_input(
                "Mevcut Şifre:",
                type="password",
                help="Mevcut şifrenizi girin"
            )
            
            new_password = st.text_input(
                "Yeni Şifre:",
                type="password",
                help="Yeni şifrenizi girin"
            )
        
        with col_pass2:
            confirm_password = st.text_input(
                "Yeni Şifre Tekrar:",
                type="password",
                help="Yeni şifrenizi tekrar girin"
            )
        
        # Submit button
        col_cancel, col_save = st.columns([1, 2])
        
        with col_cancel:
            cancel_edit = st.form_submit_button("❌ İptal", use_container_width=True)
        
        with col_save:
            save_profile = st.form_submit_button(
                "💾 Kaydet",
                use_container_width=True,
                type="primary"
            )
        
        if save_profile:
            errors = []
            
            # Validate inputs
            if not new_full_name.strip():
                errors.append("Ad soyad boş olamaz!")
            
            if not validate_email(new_email):
                errors.append("Geçerli bir e-posta adresi girin!")
            
            if not validate_phone(new_phone):
                errors.append("Geçerli bir telefon numarası girin!")
            
            # Password validation
            if current_password or new_password or confirm_password:
                if not current_password:
                    errors.append("Şifre değiştirmek için mevcut şifrenizi girin!")
                elif not new_password:
                    errors.append("Yeni şifre boş olamaz!")
                elif len(new_password) < 6:
                    errors.append("Yeni şifre en az 6 karakter olmalıdır!")
                elif new_password != confirm_password:
                    errors.append("Yeni şifreler eşleşmiyor!")
            
            if errors:
                for error in errors:
                    display_error_message(error)
            else:
                # Update user info (demo)
                user.full_name = new_full_name.strip()
                user.email = new_email.strip()
                user.phone = new_phone.strip()
                
                display_success_message("Profil bilgileriniz başarıyla güncellendi!")
    
    st.markdown("---")
    
    # Settings section
    st.markdown("### ⚙️ Ayarlar")
    
    # Language settings
    st.markdown("**🌐 Dil Ayarları**")
    
    language_options = {
        "tr": "🇹🇷 Türkçe",
        "en": "🇺🇸 English",
        "ar": "🇸🇦 العربية"
    }
    
    selected_language = st.selectbox(
        "Dil seçin:",
        options=list(language_options.keys()),
        format_func=lambda x: language_options[x],
        index=0,  # Default to Turkish
        help="Uygulama dilini seçin"
    )
    
    if selected_language != "tr":
        st.info("🔄 Çoklu dil desteği yakında eklenecek!")
    
    # Notification settings
    st.markdown("**🔔 Bildirim Ayarları**")
    
    col_notif1, col_notif2 = st.columns(2)
    
    with col_notif1:
        email_notifications = st.checkbox(
            "📧 E-posta bildirimleri",
            value=True,
            help="İhbar durumu güncellemelerini e-posta ile alın"
        )
        
        push_notifications = st.checkbox(
            "📱 Push bildirimleri",
            value=True,
            help="Anlık bildirimler alın"
        )
    
    with col_notif2:
        sms_notifications = st.checkbox(
            "💬 SMS bildirimleri",
            value=False,
            help="Önemli güncellemeleri SMS ile alın"
        )
        
        marketing_emails = st.checkbox(
            "📢 Pazarlama e-postaları",
            value=False,
            help="Kampanya ve duyuru e-postalarını alın"
        )
    
    # Privacy settings
    st.markdown("**🔒 Gizlilik Ayarları**")
    
    location_sharing = st.checkbox(
        "📍 Konum paylaşımı",
        value=True,
        help="İhbar oluştururken konum bilgisi paylaşın"
    )
    
    profile_visibility = st.selectbox(
        "👤 Profil görünürlüğü:",
        ["Herkese açık", "Sadece memurlar", "Gizli"],
        index=1,
        help="Profilinizin kimler tarafından görülebileceğini seçin"
    )
    
    # Save settings button
    if st.button("💾 Ayarları Kaydet", use_container_width=True):
        display_success_message("Ayarlarınız başarıyla kaydedildi!")
    
    st.markdown("---")
    
    # Menu items
    st.markdown("### 📋 Menü")
    
    menu_items = [
        ("📊", "İstatistiklerim", "reports", "Detaylı ihbar istatistiklerini görüntüle"),
        ("💰", "Cüzdanım", "wallet", "Kazançlarınızı ve para çekme işlemlerini yönetin"),
        ("🔔", "Bildirimler", None, "Bildirim geçmişinizi görüntüleyin"),
        ("❓", "Yardım", None, "Sık sorulan sorular ve destek"),
        ("📋", "Kullanım Şartları", None, "Uygulama kullanım şartlarını okuyun"),
        ("🔒", "Gizlilik Politikası", None, "Gizlilik politikamızı inceleyin"),
    ]
    
    for icon, title, page, description in menu_items:
        if st.button(f"{icon} {title}", use_container_width=True, help=description):
            if page:
                navigate_to(page)
            else:
                st.info(f"🔄 {title} özelliği yakında eklenecek!")
    
    st.markdown("---")
    
    # App info
    st.markdown("### ℹ️ Uygulama Bilgileri")
    
    st.markdown(f"""
    <div class="card">
        <div style="text-align: center;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">🛡️</div>
            <h3 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">{APP_NAME}</h3>
            <p style="color: #666; margin-bottom: 1rem;">Vatandaş İhbar Uygulaması</p>
            <p style="color: #999; font-size: 0.875rem;">Sürüm: {APP_VERSION}</p>
        </div>
    </div>
    """, unsafe_allow_html=True)
    
    # Logout section
    st.markdown("---")
    st.markdown("### 🚪 Çıkış")
    
    col_logout1, col_logout2 = st.columns([2, 1])
    
    with col_logout1:
        st.markdown("""
        <div style="padding: 1rem; background-color: #f8f9fa; border-radius: 8px;">
            <p style="margin: 0; color: #666;">
                Hesabınızdan çıkış yapmak istediğinizden emin misiniz?
            </p>
        </div>
        """, unsafe_allow_html=True)
    
    with col_logout2:
        if st.button("🚪 Çıkış Yap", use_container_width=True, type="secondary"):
            logout_user()
            display_success_message("Başarıyla çıkış yaptınız!")
            st.rerun()
    
    # Footer
    st.markdown("---")
    st.markdown(f"""
    <div style="text-align: center; color: #999; font-size: 0.875rem; padding: 1rem;">
        © 2024 {APP_NAME} - Tüm hakları saklıdır<br>
        Toplumsal güvenlik için birlikte çalışıyoruz 🤝
    </div>
    """, unsafe_allow_html=True)
