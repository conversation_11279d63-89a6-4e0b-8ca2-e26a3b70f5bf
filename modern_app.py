#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرشد - تطبيق حديث وجميل
تطبيق نافذة GUI حديثة بتصميم جميل
"""

try:
    import customtkinter as ctk
    MODERN_UI = True
except ImportError:
    import tkinter as tk
    import tkinter.ttk as ttk
    from tkinter import messagebox
    MODERN_UI = False

import sqlite3
import hashlib
from datetime import datetime
import os

class ModernMurshidApp:
    def __init__(self):
        if MODERN_UI:
            # استخدام CustomTkinter للمظهر الحديث
            ctk.set_appearance_mode("light")
            ctk.set_default_color_theme("blue")
            self.root = ctk.CTk()
        else:
            # استخدام Tkinter العادي كبديل
            self.root = tk.Tk()

        self.setup_window()
        self.setup_database()
        self.current_user = None
        self.show_login_page()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🛡️ مرشد - تطبيق الإبلاغ عن المخالفات")
        self.root.geometry("360x640")
        self.root.resizable(False, False)

        # توسيط النافذة
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (360 // 2)
        y = (self.root.winfo_screenheight() // 2) - (640 // 2)
        self.root.geometry(f"360x640+{x}+{y}")

        # الألوان الحديثة
        self.colors = {
            'primary': '#2196F3',
            'primary_dark': '#1976D2',
            'success': '#4CAF50',
            'error': '#F44336',
            'warning': '#FF9800',
            'background': '#FAFAFA',
            'surface': '#FFFFFF',
            'text': '#212121',
            'text_secondary': '#757575'
        }

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
            VALUES (?, ?, ?, ?)
        ''', ("<EMAIL>", admin_password, "System Administrator", True))

        conn.commit()
        conn.close()

    def hash_password(self, password):
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate_user(self, email, password):
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        hashed_password = self.hash_password(password)
        cursor.execute('''
            SELECT id, email, full_name, is_admin
            FROM users
            WHERE email = ? AND password = ?
        ''', (email, hashed_password))

        user = cursor.fetchone()
        conn.close()

        if user:
            return {
                'id': user[0],
                'email': user[1],
                'full_name': user[2],
                'is_admin': bool(user[3])
            }
        return None

    def register_user(self, email, password, full_name, phone=None):
        try:
            conn = sqlite3.connect('mursid.db')
            cursor = conn.cursor()

            hashed_password = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (email, password, full_name, phone)
                VALUES (?, ?, ?, ?)
            ''', (email, hashed_password, full_name, phone))

            conn.commit()
            conn.close()
            return True, "تم التسجيل بنجاح!"

        except sqlite3.IntegrityError:
            return False, "هذا البريد الإلكتروني مستخدم بالفعل!"
        except Exception as e:
            return False, f"خطأ في التسجيل: {str(e)}"

    def clear_window(self):
        for widget in self.root.winfo_children():
            widget.destroy()

    def show_message(self, title, message, type="info"):
        """عرض رسالة جميلة"""
        if MODERN_UI:
            # استخدام نافذة مخصصة حديثة
            dialog = ctk.CTkToplevel(self.root)
            dialog.title(title)
            dialog.geometry("300x150")
            dialog.resizable(False, False)

            # توسيط النافذة
            dialog.transient(self.root)
            dialog.grab_set()

            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 150
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 75
            dialog.geometry(f"300x150+{x}+{y}")

            # المحتوى
            frame = ctk.CTkFrame(dialog)
            frame.pack(fill="both", expand=True, padx=20, pady=20)

            # الأيقونة والرسالة
            icon = "✅" if type == "success" else "❌" if type == "error" else "ℹ️"

            ctk.CTkLabel(
                frame,
                text=f"{icon} {title}",
                font=ctk.CTkFont(size=16, weight="bold")
            ).pack(pady=(10, 5))

            ctk.CTkLabel(
                frame,
                text=message,
                font=ctk.CTkFont(size=12),
                wraplength=250
            ).pack(pady=(0, 10))

            ctk.CTkButton(
                frame,
                text="موافق",
                command=dialog.destroy,
                width=100
            ).pack(pady=(10, 0))

        else:
            # استخدام messagebox العادي
            if type == "success":
                messagebox.showinfo(title, message)
            elif type == "error":
                messagebox.showerror(title, message)
            else:
                messagebox.showinfo(title, message)

    def show_login_page(self):
        """صفحة تسجيل الدخول الحديثة"""
        self.clear_window()

        if MODERN_UI:
            # الخلفية الرئيسية
            main_frame = ctk.CTkFrame(self.root, fg_color=self.colors['background'])
            main_frame.pack(fill="both", expand=True)

            # العنوان الرئيسي
            title_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            title_frame.pack(pady=(40, 20))

            ctk.CTkLabel(
                title_frame,
                text="🛡️",
                font=ctk.CTkFont(size=48)
            ).pack()

            ctk.CTkLabel(
                title_frame,
                text="مرشد",
                font=ctk.CTkFont(size=24, weight="bold"),
                text_color=self.colors['primary']
            ).pack()

            ctk.CTkLabel(
                title_frame,
                text="تطبيق الإبلاغ عن المخالفات",
                font=ctk.CTkFont(size=14),
                text_color=self.colors['text_secondary']
            ).pack()

            # بطاقة تسجيل الدخول
            login_frame = ctk.CTkFrame(main_frame, width=300, height=350)
            login_frame.pack(pady=20, padx=30)
            login_frame.pack_propagate(False)

            # عنوان تسجيل الدخول
            ctk.CTkLabel(
                login_frame,
                text="🔐 تسجيل الدخول",
                font=ctk.CTkFont(size=18, weight="bold")
            ).pack(pady=(20, 15))

            # حقل البريد الإلكتروني
            ctk.CTkLabel(
                login_frame,
                text="📧 البريد الإلكتروني",
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", padx=20, pady=(10, 5))

            self.email_entry = ctk.CTkEntry(
                login_frame,
                placeholder_text="<EMAIL>",
                width=260,
                height=35
            )
            self.email_entry.pack(padx=20, pady=(0, 10))
            self.email_entry.insert(0, "<EMAIL>")

            # حقل كلمة المرور
            ctk.CTkLabel(
                login_frame,
                text="🔒 كلمة المرور",
                font=ctk.CTkFont(size=12)
            ).pack(anchor="w", padx=20, pady=(0, 5))

            self.password_entry = ctk.CTkEntry(
                login_frame,
                placeholder_text="كلمة المرور",
                show="*",
                width=260,
                height=35
            )
            self.password_entry.pack(padx=20, pady=(0, 15))
            self.password_entry.insert(0, "admin123")

            # أزرار
            ctk.CTkButton(
                login_frame,
                text="🚀 دخول",
                command=self.login,
                width=260,
                height=40,
                font=ctk.CTkFont(size=14, weight="bold")
            ).pack(padx=20, pady=(0, 10))

            ctk.CTkButton(
                login_frame,
                text="📝 تسجيل جديد",
                command=self.show_register_page,
                width=260,
                height=35,
                fg_color=self.colors['success'],
                hover_color=self.colors['success'],
                font=ctk.CTkFont(size=12)
            ).pack(padx=20, pady=(0, 20))

            # معلومات تجريبية
            info_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            info_frame.pack(pady=10)

            info_text = """معلومات تجريبية:
👑 حساب الأدمن: <EMAIL> / admin123
📝 أو أنشئ حساب جديد"""

            ctk.CTkLabel(
                info_frame,
                text=info_text,
                font=ctk.CTkFont(size=11),
                text_color=self.colors['text_secondary'],
                justify="center"
            ).pack()

        else:
            # نسخة Tkinter العادية (مبسطة)
            self.show_simple_login()

    def show_simple_login(self):
        """نسخة مبسطة لـ Tkinter العادي"""
        frame = tk.Frame(self.root, bg='#f0f0f0')
        frame.pack(fill="both", expand=True, padx=20, pady=20)

        tk.Label(frame, text="🛡️ مرشد", font=("Arial", 20, "bold"), bg='#f0f0f0').pack(pady=20)

        tk.Label(frame, text="📧 البريد الإلكتروني:", bg='#f0f0f0').pack(anchor="w", pady=(10, 5))
        self.email_entry = tk.Entry(frame, width=30, font=("Arial", 10))
        self.email_entry.pack(pady=(0, 10), ipady=5)
        self.email_entry.insert(0, "<EMAIL>")

        tk.Label(frame, text="🔒 كلمة المرور:", bg='#f0f0f0').pack(anchor="w", pady=(0, 5))
        self.password_entry = tk.Entry(frame, width=30, show="*", font=("Arial", 10))
        self.password_entry.pack(pady=(0, 15), ipady=5)
        self.password_entry.insert(0, "admin123")

        tk.Button(frame, text="🚀 دخول", command=self.login, bg='#2196F3', fg='white',
                 font=("Arial", 12, "bold"), relief="flat").pack(fill="x", pady=5, ipady=8)

        tk.Button(frame, text="📝 تسجيل جديد", command=self.show_register_page, bg='#4CAF50', fg='white',
                 font=("Arial", 10), relief="flat").pack(fill="x", pady=5, ipady=6)

    def login(self):
        """تسجيل الدخول"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        if not email or not password:
            self.show_message("خطأ", "يرجى ملء جميع الحقول!", "error")
            return

        user = self.authenticate_user(email, password)
        if user:
            self.current_user = user
            self.show_message("نجح", "تم تسجيل الدخول بنجاح!", "success")
            self.root.after(1000, self.show_main_page)  # انتظار ثانية ثم الانتقال
        else:
            self.show_message("خطأ", "بيانات خاطئة!", "error")

    def show_register_page(self):
        """صفحة التسجيل الحديثة"""
        self.clear_window()

        if MODERN_UI:
            # الخلفية الرئيسية
            main_frame = ctk.CTkFrame(self.root, fg_color=self.colors['background'])
            main_frame.pack(fill="both", expand=True)

            # العنوان
            title_frame = ctk.CTkFrame(main_frame, fg_color="transparent")
            title_frame.pack(pady=(20, 10))

            ctk.CTkLabel(
                title_frame,
                text="📝 تسجيل حساب جديد",
                font=ctk.CTkFont(size=20, weight="bold"),
                text_color=self.colors['primary']
            ).pack()

            # بطاقة التسجيل
            register_frame = ctk.CTkFrame(main_frame, width=300)
            register_frame.pack(pady=10, padx=30, fill="y", expand=True)

            # الحقول
            fields = [
                ("👤 الاسم الكامل", "full_name", False),
                ("📧 البريد الإلكتروني", "email", False),
                ("📱 رقم الهاتف", "phone", False),
                ("🔒 كلمة المرور", "password", True),
                ("🔒 تأكيد كلمة المرور", "confirm_password", True)
            ]

            self.register_entries = {}

            for label_text, field_name, is_password in fields:
                ctk.CTkLabel(
                    register_frame,
                    text=label_text,
                    font=ctk.CTkFont(size=12)
                ).pack(anchor="w", padx=20, pady=(10, 5))

                entry = ctk.CTkEntry(
                    register_frame,
                    width=260,
                    height=35,
                    show="*" if is_password else ""
                )
                entry.pack(padx=20, pady=(0, 5))
                self.register_entries[field_name] = entry

            # أزرار
            ctk.CTkButton(
                register_frame,
                text="✅ تسجيل",
                command=self.register,
                width=260,
                height=40,
                font=ctk.CTkFont(size=14, weight="bold"),
                fg_color=self.colors['success'],
                hover_color=self.colors['success']
            ).pack(padx=20, pady=(15, 10))

            ctk.CTkButton(
                register_frame,
                text="🔙 رجوع",
                command=self.show_login_page,
                width=260,
                height=35,
                fg_color=self.colors['warning'],
                hover_color=self.colors['warning']
            ).pack(padx=20, pady=(0, 20))

        else:
            # نسخة Tkinter العادية
            self.show_simple_register()

    def show_simple_register(self):
        """نسخة مبسطة للتسجيل"""
        frame = tk.Frame(self.root, bg='#f0f0f0')
        frame.pack(fill="both", expand=True, padx=20, pady=20)

        tk.Label(frame, text="📝 تسجيل جديد", font=("Arial", 16, "bold"), bg='#f0f0f0').pack(pady=10)

        fields = [
            ("👤 الاسم الكامل:", "full_name"),
            ("📧 البريد الإلكتروني:", "email"),
            ("📱 رقم الهاتف:", "phone"),
            ("🔒 كلمة المرور:", "password"),
            ("🔒 تأكيد كلمة المرور:", "confirm_password")
        ]

        self.register_entries = {}

        for label_text, field_name in fields:
            tk.Label(frame, text=label_text, bg='#f0f0f0').pack(anchor="w", pady=(5, 2))
            entry = tk.Entry(frame, width=30, show="*" if "password" in field_name else "")
            entry.pack(pady=(0, 5), ipady=3)
            self.register_entries[field_name] = entry

        tk.Button(frame, text="✅ تسجيل", command=self.register, bg='#4CAF50', fg='white',
                 font=("Arial", 12, "bold"), relief="flat").pack(fill="x", pady=10, ipady=8)

        tk.Button(frame, text="🔙 رجوع", command=self.show_login_page, bg='#FF9800', fg='white',
                 font=("Arial", 10), relief="flat").pack(fill="x", pady=5, ipady=6)

    def register(self):
        """تسجيل مستخدم جديد"""
        full_name = self.register_entries['full_name'].get().strip()
        email = self.register_entries['email'].get().strip()
        phone = self.register_entries['phone'].get().strip()
        password = self.register_entries['password'].get().strip()
        confirm_password = self.register_entries['confirm_password'].get().strip()

        if not all([full_name, email, password, confirm_password]):
            self.show_message("خطأ", "يرجى ملء جميع الحقول المطلوبة!", "error")
            return

        if len(password) < 6:
            self.show_message("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل!", "error")
            return

        if password != confirm_password:
            self.show_message("خطأ", "كلمات المرور غير متطابقة!", "error")
            return

        success, message = self.register_user(email, password, full_name, phone)

        if success:
            self.show_message("نجح", message, "success")
            self.root.after(1500, self.show_login_page)
        else:
            self.show_message("خطأ", message, "error")

    def show_main_page(self):
        """الصفحة الرئيسية الحديثة"""
        self.clear_window()

        if MODERN_UI:
            # الخلفية الرئيسية
            main_frame = ctk.CTkFrame(self.root, fg_color=self.colors['background'])
            main_frame.pack(fill="both", expand=True)

            # الشريط العلوي
            header_frame = ctk.CTkFrame(main_frame, height=70, fg_color=self.colors['primary'])
            header_frame.pack(fill="x", padx=10, pady=(10, 0))
            header_frame.pack_propagate(False)

            # معلومات المستخدم
            user_info = f"👋 مرحباً {self.current_user['full_name']}"
            if self.current_user['is_admin']:
                user_info += " 👑"

            ctk.CTkLabel(
                header_frame,
                text=user_info,
                font=ctk.CTkFont(size=14, weight="bold"),
                text_color="white"
            ).pack(side="left", padx=15, pady=15)

            # زر تسجيل الخروج
            ctk.CTkButton(
                header_frame,
                text="🚪 خروج",
                command=self.logout,
                width=80,
                height=30,
                fg_color=self.colors['error'],
                hover_color=self.colors['error'],
                font=ctk.CTkFont(size=12)
            ).pack(side="right", padx=15, pady=15)

            # منطقة المحتوى
            content_frame = ctk.CTkScrollableFrame(main_frame)
            content_frame.pack(fill="both", expand=True, padx=10, pady=10)

            # القائمة الرئيسية
            menu_items = [
                ("🏠", "الصفحة الرئيسية", self.show_home_content, self.colors['primary']),
                ("📝", "بلاغ جديد", self.show_new_report, self.colors['success']),
                ("📋", "بلاغاتي", self.show_my_reports, self.colors['warning']),
                ("💰", "المحفظة", self.show_wallet, "#9C27B0"),
                ("👤", "الملف الشخصي", self.show_profile, "#607D8B")
            ]

            # إضافة لوحة تحكم الأدمن
            if self.current_user['is_admin']:
                menu_items.insert(0, ("👑", "لوحة التحكم", self.show_admin_dashboard, self.colors['error']))

            for icon, text, command, color in menu_items:
                btn_frame = ctk.CTkFrame(content_frame, height=60)
                btn_frame.pack(fill="x", pady=5)
                btn_frame.pack_propagate(False)

                # الأيقونة
                ctk.CTkLabel(
                    btn_frame,
                    text=icon,
                    font=ctk.CTkFont(size=24),
                    width=50
                ).pack(side="left", padx=(15, 10), pady=15)

                # النص والزر
                ctk.CTkButton(
                    btn_frame,
                    text=text,
                    command=command,
                    font=ctk.CTkFont(size=14, weight="bold"),
                    fg_color=color,
                    hover_color=color,
                    anchor="w"
                ).pack(side="left", fill="x", expand=True, padx=(0, 15), pady=10)

        else:
            # نسخة Tkinter العادية
            self.show_simple_main()

    def show_simple_main(self):
        """نسخة مبسطة للصفحة الرئيسية"""
        frame = tk.Frame(self.root, bg='#f0f0f0')
        frame.pack(fill="both", expand=True)

        # الشريط العلوي
        header = tk.Frame(frame, bg='#2196F3', height=50)
        header.pack(fill="x")
        header.pack_propagate(False)

        user_info = f"👋 {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " 👑"

        tk.Label(header, text=user_info, bg='#2196F3', fg='white',
                font=("Arial", 12, "bold")).pack(side="left", padx=15, pady=15)

        tk.Button(header, text="🚪 خروج", command=self.logout, bg='#F44336', fg='white',
                 relief="flat").pack(side="right", padx=15, pady=10)

        # القائمة
        content = tk.Frame(frame, bg='#f0f0f0')
        content.pack(fill="both", expand=True, padx=20, pady=20)

        menu_items = [
            ("🏠 الصفحة الرئيسية", self.show_home_content),
            ("📝 بلاغ جديد", self.show_new_report),
            ("📋 بلاغاتي", self.show_my_reports),
            ("💰 المحفظة", self.show_wallet),
            ("👤 الملف الشخصي", self.show_profile)
        ]

        if self.current_user['is_admin']:
            menu_items.insert(0, ("👑 لوحة التحكم", self.show_admin_dashboard))

        for text, command in menu_items:
            tk.Button(content, text=text, command=command, bg='white',
                     font=("Arial", 12, "bold"), relief="raised", bd=2,
                     anchor="w").pack(fill="x", pady=5, ipady=10)

    def show_home_content(self):
        self.show_message("الصفحة الرئيسية", "🏠 مرحباً بك في تطبيق مرشد!\n\nيمكنك الإبلاغ عن المخالفات وكسب المكافآت.")

    def show_new_report(self):
        self.show_message("بلاغ جديد", "📝 هذه الميزة قيد التطوير\n\nسيتم إضافة نموذج الإبلاغ قريباً.")

    def show_my_reports(self):
        self.show_message("بلاغاتي", "📋 هذه الميزة قيد التطوير\n\nسيتم عرض قائمة بلاغاتك هنا.")

    def show_wallet(self):
        self.show_message("المحفظة", "💰 هذه الميزة قيد التطوير\n\nسيتم عرض رصيدك والمعاملات هنا.")

    def show_profile(self):
        profile_info = f"""👤 الملف الشخصي

الاسم: {self.current_user['full_name']}
البريد: {self.current_user['email']}
النوع: {'مدير النظام' if self.current_user['is_admin'] else 'مستخدم عادي'}

هذه الميزة قيد التطوير لإضافة المزيد من التفاصيل."""

        self.show_message("الملف الشخصي", profile_info)

    def show_admin_dashboard(self):
        if not self.current_user['is_admin']:
            self.show_message("خطأ", "غير مصرح لك بالوصول لهذه الصفحة!", "error")
            return

        dashboard_info = """👑 لوحة تحكم المدير

📊 الإحصائيات:
👥 المستخدمين: 1,247
📋 البلاغات: 3,456
✅ المقبولة: 2,890
💰 المكافآت: ₺45,678

🛠️ الأدوات:
• إدارة المستخدمين
• مراجعة البلاغات
• إعدادات النظام
• التقارير والإحصائيات

هذه الميزة قيد التطوير لإضافة المزيد من الأدوات."""

        self.show_message("لوحة تحكم المدير", dashboard_info)

    def logout(self):
        if MODERN_UI:
            # نافذة تأكيد حديثة
            dialog = ctk.CTkToplevel(self.root)
            dialog.title("تسجيل الخروج")
            dialog.geometry("250x120")
            dialog.resizable(False, False)
            dialog.transient(self.root)
            dialog.grab_set()

            x = self.root.winfo_x() + (self.root.winfo_width() // 2) - 125
            y = self.root.winfo_y() + (self.root.winfo_height() // 2) - 60
            dialog.geometry(f"250x120+{x}+{y}")

            ctk.CTkLabel(dialog, text="هل تريد تسجيل الخروج؟",
                        font=ctk.CTkFont(size=14)).pack(pady=20)

            btn_frame = ctk.CTkFrame(dialog, fg_color="transparent")
            btn_frame.pack(fill="x", padx=20, pady=(0, 20))

            def confirm_logout():
                dialog.destroy()
                self.current_user = None
                self.show_login_page()

            ctk.CTkButton(btn_frame, text="نعم", command=confirm_logout,
                         width=80, fg_color=self.colors['error']).pack(side="left", padx=(0, 10))
            ctk.CTkButton(btn_frame, text="لا", command=dialog.destroy,
                         width=80).pack(side="right")
        else:
            import tkinter.messagebox as mb
            if mb.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟"):
                self.current_user = None
                self.show_login_page()

    def run(self):
        self.root.mainloop()

def main():
    print("🚀 بدء تشغيل تطبيق مرشد الحديث...")

    if MODERN_UI:
        print("✨ استخدام CustomTkinter للمظهر الحديث")
    else:
        print("⚠️ CustomTkinter غير متوفر، استخدام Tkinter العادي")
        print("💡 لتحسين المظهر، قم بتثبيت: pip install customtkinter")

    print("📱 نافذة GUI حديثة - بدون متصفح")
    print("🔧 حجم النافذة: 360x640")

    app = ModernMurshidApp()
    app.run()

if __name__ == "__main__":
    main()
