# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Wallet Page
"""

import streamlit as st
from datetime import datetime, timedelta
from utils import (
    get_user_transactions, navigate_to, display_metric_card,
    display_success_message, display_error_message, format_datetime, format_currency
)
from models import Transaction, generate_transaction_id
from config import AppColors, MINIMUM_WITHDRAWAL, WITHDRAWAL_FEE

def show_wallet_page():
    """Display wallet page"""
    
    if not st.session_state.authenticated:
        navigate_to('login')
        return
    
    user = st.session_state.current_user
    
    # Header
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">💰 Cüzdan</h1>
        <p style="color: #666; font-size: 1.1rem;">Kazançlarınızı görüntü<PERSON>in ve para çekin</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Back button
    if st.button("⬅️ Ana Sayfa", help="Ana sayfaya dön"):
        navigate_to('home')
    
    # Balance cards
    st.markdown("### 💳 Bakiye Bilgileri")
    
    col1, col2 = st.columns(2)
    
    with col1:
        display_metric_card(
            "Toplam Kazanç",
            format_currency(user.total_earnings),
            "📈",
            AppColors.SECONDARY
        )
    
    with col2:
        display_metric_card(
            "Kullanılabilir Bakiye",
            format_currency(user.available_balance),
            "💰",
            AppColors.PRIMARY
        )
    
    # Pending amount (demo)
    pending_amount = 400.0
    
    col3, col4 = st.columns(2)
    
    with col3:
        display_metric_card(
            "Bekleyen Ödül",
            format_currency(pending_amount),
            "⏳",
            AppColors.WARNING
        )
    
    with col4:
        success_rate = 85  # Demo data
        display_metric_card(
            "Başarı Oranı",
            f"%{success_rate}",
            "🎯",
            AppColors.INFO
        )
    
    st.markdown("---")
    
    # Withdrawal section
    st.markdown("### 💸 Para Çekme")
    
    # Check if withdrawal is possible
    can_withdraw = user.available_balance >= MINIMUM_WITHDRAWAL
    
    if can_withdraw:
        with st.form("withdrawal_form"):
            st.markdown(f"""
            <div style="padding: 1rem; background-color: {AppColors.INFO}20; border-radius: 8px; border-left: 4px solid {AppColors.INFO}; margin-bottom: 1rem;">
                <strong>💡 Para Çekme Bilgileri:</strong><br>
                • Minimum çekim tutarı: {format_currency(MINIMUM_WITHDRAWAL)}<br>
                • Çekim ücreti: {format_currency(WITHDRAWAL_FEE)}<br>
                • İşlem süresi: 1-3 iş günü<br>
                • Kullanılabilir bakiye: {format_currency(user.available_balance)}
            </div>
            """, unsafe_allow_html=True)
            
            # Withdrawal amount
            max_withdrawal = user.available_balance - WITHDRAWAL_FEE
            withdrawal_amount = st.number_input(
                "Çekmek istediğiniz tutar (₺):",
                min_value=MINIMUM_WITHDRAWAL,
                max_value=max_withdrawal,
                value=MINIMUM_WITHDRAWAL,
                step=10.0,
                help=f"Minimum {format_currency(MINIMUM_WITHDRAWAL)}, maksimum {format_currency(max_withdrawal)}"
            )
            
            # Bank account info (demo)
            st.markdown("**🏦 Banka Hesap Bilgileri:**")
            
            col_bank1, col_bank2 = st.columns(2)
            
            with col_bank1:
                bank_name = st.selectbox(
                    "Banka:",
                    ["Ziraat Bankası", "İş Bankası", "Garanti BBVA", "Akbank", "Yapı Kredi", "Halkbank"],
                    help="Para çekme işlemi için banka seçin"
                )
            
            with col_bank2:
                account_number = st.text_input(
                    "Hesap Numarası:",
                    placeholder="**********",
                    help="IBAN numaranızı girin"
                )
            
            # Calculate final amount
            final_amount = withdrawal_amount - WITHDRAWAL_FEE
            
            st.markdown(f"""
            <div style="padding: 1rem; background-color: {AppColors.SUCCESS}20; border-radius: 8px; border: 1px solid {AppColors.SUCCESS}; margin: 1rem 0;">
                <strong>💰 Çekim Özeti:</strong><br>
                • Çekim tutarı: {format_currency(withdrawal_amount)}<br>
                • Çekim ücreti: -{format_currency(WITHDRAWAL_FEE)}<br>
                • <strong>Hesabınıza geçecek tutar: {format_currency(final_amount)}</strong>
            </div>
            """, unsafe_allow_html=True)
            
            # Submit withdrawal
            col_cancel, col_submit = st.columns([1, 2])
            
            with col_cancel:
                cancel_withdrawal = st.form_submit_button("❌ İptal", use_container_width=True)
            
            with col_submit:
                submit_withdrawal = st.form_submit_button(
                    "💸 Para Çek",
                    use_container_width=True,
                    type="primary"
                )
            
            if submit_withdrawal:
                if not account_number:
                    display_error_message("Lütfen hesap numaranızı girin!")
                elif len(account_number) < 10:
                    display_error_message("Geçerli bir hesap numarası girin!")
                else:
                    # Process withdrawal (demo)
                    display_success_message(f"""
                    Para çekme talebiniz başarıyla alındı!
                    
                    **İşlem Detayları:**
                    • Tutar: {format_currency(final_amount)}
                    • Banka: {bank_name}
                    • Hesap: {account_number}
                    • İşlem No: TXN_{datetime.now().strftime('%Y%m%d%H%M%S')}
                    
                    Para 1-3 iş günü içinde hesabınıza geçecektir.
                    """)
                    
                    # Update user balance (demo)
                    user.available_balance -= withdrawal_amount
                    
                    st.balloons()
    
    else:
        st.markdown(f"""
        <div style="padding: 1rem; background-color: {AppColors.WARNING}20; border-radius: 8px; border-left: 4px solid {AppColors.WARNING};">
            <strong>⚠️ Para Çekme Yapılamıyor</strong><br>
            Para çekebilmek için minimum {format_currency(MINIMUM_WITHDRAWAL)} bakiyeniz olmalıdır.<br>
            Mevcut bakiyeniz: {format_currency(user.available_balance)}<br>
            Eksik tutar: {format_currency(MINIMUM_WITHDRAWAL - user.available_balance)}
        </div>
        """, unsafe_allow_html=True)
        
        st.markdown("💡 **Daha fazla kazanmak için:**")
        if st.button("🚀 Yeni İhbar Oluştur", use_container_width=True, type="primary"):
            navigate_to('new_report')
    
    st.markdown("---")
    
    # Transaction history
    st.markdown("### 📊 İşlem Geçmişi")
    
    # Get transactions
    transactions = get_user_transactions(user.id)
    
    if transactions:
        # Filter options
        col_filter1, col_filter2 = st.columns(2)
        
        with col_filter1:
            transaction_filter = st.selectbox(
                "İşlem Türü:",
                ["Tümü", "Ödül", "Para Çekme"],
                help="İşlem türüne göre filtrele"
            )
        
        with col_filter2:
            period_filter = st.selectbox(
                "Dönem:",
                ["Tümü", "Son 30 Gün", "Son 3 Ay", "Son 6 Ay"],
                help="Zaman aralığına göre filtrele"
            )
        
        # Apply filters
        filtered_transactions = transactions.copy()
        
        if transaction_filter == "Ödül":
            filtered_transactions = [t for t in filtered_transactions if t.transaction_type == "reward"]
        elif transaction_filter == "Para Çekme":
            filtered_transactions = [t for t in filtered_transactions if t.transaction_type == "withdrawal"]
        
        if period_filter != "Tümü":
            now = datetime.now()
            if period_filter == "Son 30 Gün":
                cutoff = now - timedelta(days=30)
            elif period_filter == "Son 3 Ay":
                cutoff = now - timedelta(days=90)
            elif period_filter == "Son 6 Ay":
                cutoff = now - timedelta(days=180)
            
            filtered_transactions = [t for t in filtered_transactions if t.created_at >= cutoff]
        
        # Sort by date (newest first)
        filtered_transactions.sort(key=lambda x: x.created_at, reverse=True)
        
        # Display transactions
        if filtered_transactions:
            for transaction in filtered_transactions:
                transaction_icon = "💰" if transaction.amount > 0 else "💸"
                transaction_color = AppColors.SUCCESS if transaction.amount > 0 else AppColors.ERROR
                amount_text = f"+{format_currency(transaction.amount)}" if transaction.amount > 0 else format_currency(transaction.amount)
                
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem; border-left: 4px solid {transaction_color};">
                    <div style="display: flex; justify-content: space-between; align-items: center;">
                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                            <span style="font-size: 1.5rem;">{transaction_icon}</span>
                            <div>
                                <div style="font-weight: 500;">{transaction.description}</div>
                                <div style="font-size: 0.875rem; color: #666;">
                                    {format_datetime(transaction.created_at)} • #{transaction.id}
                                </div>
                            </div>
                        </div>
                        <div style="text-align: right;">
                            <div style="font-weight: bold; font-size: 1.1rem; color: {transaction_color};">
                                {amount_text}
                            </div>
                        </div>
                    </div>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.info("Seçilen filtrelere uygun işlem bulunamadı.")
    
    else:
        st.markdown(f"""
        <div style="padding: 2rem; text-align: center; background-color: #f8f9fa; border-radius: 8px;">
            <div style="font-size: 3rem; margin-bottom: 1rem;">📊</div>
            <h3 style="color: #666; margin-bottom: 1rem;">Henüz İşlem Geçmişiniz Yok</h3>
            <p style="color: #999; margin-bottom: 2rem;">
                İhbar oluşturup onaylandığında burada ödül işlemlerinizi görebilirsiniz.
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        if st.button("🚀 İlk İhbarımı Oluştur", use_container_width=True, type="primary"):
            navigate_to('new_report')
    
    st.markdown("---")
    
    # Earnings statistics
    st.markdown("### 📈 Kazanç İstatistikleri")
    
    # Demo statistics
    monthly_earnings = [120, 180, 250, 200, 300, 200]  # Last 6 months
    months = ["Ocak", "Şubat", "Mart", "Nisan", "Mayıs", "Haziran"]
    
    # Simple chart using metrics
    st.markdown("**Son 6 Aylık Kazanç Trendi:**")
    
    for i in range(0, len(months), 3):
        cols = st.columns(3)
        for j in range(3):
            if i + j < len(months):
                with cols[j]:
                    st.metric(
                        months[i + j],
                        format_currency(monthly_earnings[i + j]),
                        delta=f"{monthly_earnings[i + j] - (monthly_earnings[i + j - 1] if i + j > 0 else monthly_earnings[i + j]):.0f}" if i + j > 0 else None
                    )
    
    # Tips for earning more
    st.markdown("---")
    st.markdown("### 💡 Daha Fazla Kazanmak İçin İpuçları")
    
    tips = [
        ("📸", "Kaliteli Fotoğraf", "Net ve açık fotoğraflar çekin"),
        ("📝", "Detaylı Açıklama", "İhlali detaylı bir şekilde açıklayın"),
        ("⏰", "Hızlı Bildirim", "İhlali gördüğünüz anda bildirin"),
        ("📍", "Doğru Konum", "Konum bilgisinin doğru olduğundan emin olun")
    ]
    
    for i in range(0, len(tips), 2):
        col_tip1, col_tip2 = st.columns(2)
        
        with col_tip1:
            if i < len(tips):
                icon, title, desc = tips[i]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">{icon}</span>
                        <strong style="color: {AppColors.PRIMARY};">{title}</strong>
                    </div>
                    <div style="font-size: 0.875rem; color: #666;">{desc}</div>
                </div>
                """, unsafe_allow_html=True)
        
        with col_tip2:
            if i + 1 < len(tips):
                icon, title, desc = tips[i + 1]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                    <div style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.5rem;">
                        <span style="font-size: 1.2rem;">{icon}</span>
                        <strong style="color: {AppColors.PRIMARY};">{title}</strong>
                    </div>
                    <div style="font-size: 0.875rem; color: #666;">{desc}</div>
                </div>
                """, unsafe_allow_html=True)
