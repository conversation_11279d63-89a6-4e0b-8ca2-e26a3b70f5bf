# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Login Page
"""

import streamlit as st
from utils import authenticate_user, login_user, validate_email, display_error_message, display_info_message
from config import AppColors, APP_NAME

def show_login_page():
    """Display login page"""

    # Center the login form
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # Logo and title - محسن للهواتف
        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 1rem; padding-top: 0.5rem;">
            <div style="
                width: 80px;
                height: 80px;
                background-color: {AppColors.PRIMARY};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 0.75rem auto;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ">
                <span style="font-size: 2rem; color: white;">🛡️</span>
            </div>
            <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.25rem; font-weight: bold; font-size: 1.5rem;">{APP_NAME}</h1>
            <p style="color: #666; font-size: 0.9rem; margin-bottom: 0;">Hoş Geldiniz</p>
        </div>
        """, unsafe_allow_html=True)

        # Login form - محسن للهواتف
        with st.form("login_form", clear_on_submit=False):
            st.markdown('<h3 style="margin-bottom: 0.5rem; color: #333;">🔐 Giriş Yap</h3>', unsafe_allow_html=True)

            email = st.text_input(
                "📧 E-posta",
                placeholder="<EMAIL>",
                help="E-posta adresinizi girin",
                label_visibility="visible"
            )

            password = st.text_input(
                "🔒 Şifre",
                type="password",
                placeholder="Şifrenizi girin",
                help="Şifrenizi girin",
                label_visibility="visible"
            )

            col_login, col_forgot = st.columns([2, 1])

            with col_login:
                login_button = st.form_submit_button(
                    "🚀 Giriş Yap",
                    use_container_width=True,
                    type="primary"
                )

            with col_forgot:
                forgot_button = st.form_submit_button(
                    "❓ Şifremi Unuttum",
                    use_container_width=True
                )

            if login_button:
                if not email or not password:
                    display_error_message("Lütfen tüm alanları doldurun!")
                elif not validate_email(email):
                    display_error_message("Geçerli bir e-posta adresi girin!")
                else:
                    # Authenticate user
                    user = authenticate_user(email, password)
                    if user:
                        login_user(user)
                        display_info_message("Giriş başarılı! Yönlendiriliyorsunuz...")
                        st.rerun()
                    else:
                        display_error_message("Geçersiz e-posta veya şifre!")

            if forgot_button:
                st.info("🔄 Şifre sıfırlama özelliği yakında eklenecek!")

        # Demo info - مضغوط للهواتف
        st.markdown('<div style="margin: 0.5rem 0;"></div>', unsafe_allow_html=True)

        with st.expander("ℹ️ Demo Bilgileri", expanded=False):
            st.markdown("""
            **Demo Bilgileri:**
            - Herhangi bir e-posta ve şifre ile giriş yapabilirsiniz
            - Örnek: <EMAIL> / 123456
            - Bu demo sürümünde tüm özellikler test edilebilir
            """)

        # Register link - مضغوط
        st.markdown(f"""
        <div style="text-align: center; margin-top: 1rem; padding: 0.75rem; background-color: #f8f9fa; border-radius: 8px;">
            <p style="margin: 0; color: #666; font-size: 0.9rem;">Hesabınız yok mu?</p>
            <p style="margin: 0.25rem 0 0 0;">
                <a href="#" style="color: {AppColors.PRIMARY}; text-decoration: none; font-weight: 500; font-size: 0.9rem;">
                    📝 Kayıt Ol
                </a>
            </p>
        </div>
        """, unsafe_allow_html=True)

        # Features preview - في expander لتوفير المساحة
        with st.expander("✨ Özellikler", expanded=False):
            features = [
                ("🚗", "Trafik İhlalleri", "Kırmızı ışık, hız limiti ihlalleri"),
                ("🗑️", "Temizlik İhlalleri", "Çöp atma, kirletme"),
                ("⚠️", "Güvenlik İhlalleri", "Taciz, şiddet olayları"),
                ("🅿️", "Park İhlalleri", "Yasak yerlere park etme"),
                ("💰", "Ödül Sistemi", "Onaylanan ihbarlardan %20 ödül"),
                ("📱", "Kolay Kullanım", "Tek tıkla ihbar oluşturma")
            ]

            for i in range(0, len(features), 2):
                col_feat1, col_feat2 = st.columns(2)

                with col_feat1:
                    if i < len(features):
                        icon, title, desc = features[i]
                        st.markdown(f"""
                        <div style="text-align: center; padding: 0.75rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                            <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">{icon}</div>
                            <div style="font-weight: 500; color: {AppColors.PRIMARY}; margin-bottom: 0.25rem; font-size: 0.9rem;">{title}</div>
                            <div style="font-size: 0.75rem; color: #666;">{desc}</div>
                        </div>
                        """, unsafe_allow_html=True)

                with col_feat2:
                    if i + 1 < len(features):
                        icon, title, desc = features[i + 1]
                        st.markdown(f"""
                        <div style="text-align: center; padding: 0.75rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                            <div style="font-size: 1.5rem; margin-bottom: 0.25rem;">{icon}</div>
                            <div style="font-weight: 500; color: {AppColors.PRIMARY}; margin-bottom: 0.25rem; font-size: 0.9rem;">{title}</div>
                            <div style="font-size: 0.75rem; color: #666;">{desc}</div>
                        </div>
                        """, unsafe_allow_html=True)

def show_register_page():
    """Display register page (placeholder)"""
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 2rem;">
            <div style="
                width: 120px;
                height: 120px;
                background-color: {AppColors.SECONDARY};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 1.5rem auto;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            ">
                <span style="font-size: 3rem; color: white;">📝</span>
            </div>
            <h1 style="color: {AppColors.SECONDARY}; margin-bottom: 0.5rem; font-weight: bold;">Kayıt Ol</h1>
            <p style="color: #666; font-size: 1.1rem;">Yeni Hesap Oluştur</p>
        </div>
        """, unsafe_allow_html=True)

        with st.form("register_form"):
            st.text_input("👤 Ad Soyad", placeholder="Adınız ve soyadınız")
            st.text_input("📧 E-posta", placeholder="<EMAIL>")
            st.text_input("📱 Telefon", placeholder="+90 ************")
            st.text_input("🔒 Şifre", type="password", placeholder="Güçlü bir şifre seçin")
            st.text_input("🔒 Şifre Tekrar", type="password", placeholder="Şifrenizi tekrar girin")

            st.checkbox("📋 Kullanım şartlarını ve gizlilik politikasını kabul ediyorum")

            register_button = st.form_submit_button(
                "✅ Kayıt Ol",
                use_container_width=True,
                type="primary"
            )

            if register_button:
                st.info("🔄 Kayıt özelliği yakında eklenecek!")

        st.markdown(f"""
        <div style="text-align: center; margin-top: 2rem;">
            <p style="color: #666;">Zaten hesabınız var mı?</p>
            <a href="#" style="color: {AppColors.PRIMARY}; text-decoration: none; font-weight: 500;">
                🔐 Giriş Yap
            </a>
        </div>
        """, unsafe_allow_html=True)
