# -*- coding: utf-8 -*-
"""
Register Page - صفحة التسجيل
تسجيل مستخدم جديد
"""

import streamlit as st
from utils import register_user, validate_email, display_error_message, display_success_message
from config import AppColors, APP_NAME
from language_manager import get_text, apply_rtl_css

def show_register_page():
    """عرض صفحة التسجيل"""

    # تطبيق CSS للغات RTL
    apply_rtl_css()

    # تخطيط الصفحة
    col1, col2, col3 = st.columns([1, 2, 1])

    with col2:
        # اللوجو والعنوان
        st.markdown(f"""
        <div style="text-align: center; margin-bottom: 1rem; padding-top: 0.25rem;">
            <div style="
                width: 80px;
                height: 80px;
                background-color: {AppColors.SECONDARY};
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 0.5rem auto;
                box-shadow: 0 3px 10px rgba(0,0,0,0.2);
            ">
                <span style="font-size: 2rem; color: white;">📝</span>
            </div>
            <h1 style="color: {AppColors.SECONDARY}; margin-bottom: 0.1rem; font-weight: bold; font-size: 1.4rem;">{get_text('register', 'Kayıt Ol')}</h1>
            <p style="color: #666; font-size: 0.85rem; margin-bottom: 0;">Yeni Hesap Oluştur</p>
        </div>
        """, unsafe_allow_html=True)

        # نموذج التسجيل
        with st.form("register_form", clear_on_submit=False):
            st.markdown(f'<h3 style="margin: 0 0 0.25rem 0; color: #333; font-size: 1.1rem;">📝 {get_text("register", "Kayıt Ol")}</h3>', unsafe_allow_html=True)

            # مسافة صغيرة
            st.markdown('<div style="margin-bottom: 0.25rem;"></div>', unsafe_allow_html=True)

            # الحقول
            full_name = st.text_input(
                f"👤 {get_text('full_name', 'Ad Soyad')}",
                placeholder=get_text('placeholders.full_name', 'Adınız ve soyadınız'),
                help="Tam adınızı ve soyadınızı girin"
            )

            email = st.text_input(
                f"📧 {get_text('email', 'E-posta')}",
                placeholder=get_text('placeholders.email', '<EMAIL>'),
                help="Geçerli bir e-posta adresi girin"
            )

            phone = st.text_input(
                f"📱 {get_text('phone', 'Telefon')}",
                placeholder=get_text('placeholders.phone', '+90 ************'),
                help="Telefon numaranızı girin (isteğe bağlı)"
            )

            password = st.text_input(
                f"🔒 {get_text('password', 'Şifre')}",
                type="password",
                placeholder="Güçlü bir şifre seçin",
                help="En az 6 karakter uzunluğunda olmalı"
            )

            confirm_password = st.text_input(
                f"🔒 {get_text('confirm_password', 'Şifreyi Onayla')}",
                type="password",
                placeholder="Şifrenizi tekrar girin",
                help="Yukarıdaki şifreyi tekrar girin"
            )

            # Şartları kabul etme
            terms_accepted = st.checkbox(
                "📋 Kullanım şartlarını ve gizlilik politikasını kabul ediyorum",
                help="Devam etmek için şartları kabul etmelisiniz"
            )

            # Kayıt butonu
            col_register, col_back = st.columns([2, 1])

            with col_register:
                register_button = st.form_submit_button(
                    "✅ Kayıt Ol",
                    use_container_width=True,
                    type="primary"
                )

            with col_back:
                back_button = st.form_submit_button(
                    "🔙 Geri",
                    use_container_width=True
                )

            # Form işleme
            if register_button:
                # Doğrulama
                if not full_name or not email or not password or not confirm_password:
                    display_error_message("Lütfen tüm zorunlu alanları doldurun!")
                elif not validate_email(email):
                    display_error_message("Geçerli bir e-posta adresi girin!")
                elif len(password) < 6:
                    display_error_message("Şifre en az 6 karakter uzunluğunda olmalı!")
                elif password != confirm_password:
                    display_error_message("Şifreler eşleşmiyor!")
                elif not terms_accepted:
                    display_error_message("Kullanım şartlarını kabul etmelisiniz!")
                else:
                    # Kayıt işlemi
                    try:
                        if register_user(email, password, full_name, phone):
                            display_success_message("Kayıt başarılı! Şimdi giriş yapabilirsiniz.")
                            st.session_state.show_register = False
                            st.rerun()
                        else:
                            display_error_message("Bu e-posta adresi zaten kullanılıyor!")
                    except Exception as e:
                        display_error_message(f"Kayıt sırasında hata oluştu: {str(e)}")

            if back_button:
                st.session_state.show_register = False
                st.rerun()

        # Giriş yap linki
        st.markdown(f"""
        <div style="text-align: center; margin-top: 1rem; padding: 0.5rem; background-color: #f8f9fa; border-radius: 6px;">
            <p style="margin: 0; color: #666; font-size: 0.8rem;">Zaten hesabınız var mı?</p>
            <p style="margin: 0.1rem 0 0 0;">
                <a href="#" style="color: {AppColors.PRIMARY}; text-decoration: none; font-weight: 500; font-size: 0.8rem;">
                    🔐 Giriş Yap
                </a>
            </p>
        </div>
        """, unsafe_allow_html=True)

if __name__ == "__main__":
    show_register_page()
