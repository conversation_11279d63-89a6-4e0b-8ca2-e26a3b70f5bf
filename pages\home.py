# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Home Page
"""

import streamlit as st
from utils import (
    calculate_user_stats, display_metric_card, navigate_to,
    get_user_reports, display_info_message
)
from config import ViolationType, VIOLATION_CONFIG, AppColors, APP_NAME

def show_home_page():
    """Display home page"""
    
    if not st.session_state.authenticated:
        navigate_to('login')
        return
    
    user = st.session_state.current_user
    
    # Header
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">🏠 {APP_NAME}</h1>
        <p style="color: #666; font-size: 1.1rem;"><PERSON><PERSON> geldiniz, {user.full_name}!</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Welcome card
    st.markdown(f"""
    <div class="card">
        <h3 style="color: {AppColors.PRIMARY}; margin-bottom: 1rem;">🎯 Hoş Geldiniz</h3>
        <p style="color: #666; line-height: 1.6;">
            Toplumsal güvenlik için birlikte çalışalım. İhlalleri bildirerek hem topluma katkıda bulunun hem de ödül kazanın.
            Her onaylanan ihbar için %20 ödül alırsınız!
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Quick report button
    st.markdown("### 📸 Hızlı İhbar")
    
    if st.button(
        "🚀 Şimdi İhbar Et",
        use_container_width=True,
        type="primary",
        help="Yeni bir ihbar oluşturmak için tıklayın"
    ):
        navigate_to('new_report')
    
    st.markdown("---")
    
    # Violation types grid
    st.markdown("### 📋 İhlal Türleri")
    
    # Create 2x3 grid for violation types
    violation_types = [
        ViolationType.TRAFFIC,
        ViolationType.CLEANLINESS,
        ViolationType.HARASSMENT,
        ViolationType.VIOLENCE,
        ViolationType.NOISE,
        ViolationType.PARKING
    ]
    
    for i in range(0, len(violation_types), 2):
        col1, col2 = st.columns(2)
        
        with col1:
            if i < len(violation_types):
                violation_type = violation_types[i]
                config = VIOLATION_CONFIG[violation_type]
                
                if st.button(
                    f"{config['icon']} {config['name_tr']}",
                    key=f"violation_{violation_type.value}",
                    use_container_width=True,
                    help=config['description']
                ):
                    # Set selected violation type and navigate to new report
                    st.session_state.selected_violation_type = violation_type
                    navigate_to('new_report')
        
        with col2:
            if i + 1 < len(violation_types):
                violation_type = violation_types[i + 1]
                config = VIOLATION_CONFIG[violation_type]
                
                if st.button(
                    f"{config['icon']} {config['name_tr']}",
                    key=f"violation_{violation_type.value}",
                    use_container_width=True,
                    help=config['description']
                ):
                    # Set selected violation type and navigate to new report
                    st.session_state.selected_violation_type = violation_type
                    navigate_to('new_report')
    
    st.markdown("---")
    
    # User statistics
    st.markdown("### 📊 İstatistiklerim")
    
    stats = calculate_user_stats(user.id)
    
    col1, col2 = st.columns(2)
    
    with col1:
        display_metric_card(
            "Toplam İhbar",
            str(stats['total_reports']),
            "📋",
            AppColors.PRIMARY
        )
    
    with col2:
        display_metric_card(
            "Toplam Kazanç",
            f"₺{stats['total_earnings']:.0f}",
            "💰",
            AppColors.SECONDARY
        )
    
    # Additional stats
    col3, col4 = st.columns(2)
    
    with col3:
        display_metric_card(
            "Onaylanan",
            str(stats['approved_reports']),
            "✅",
            AppColors.APPROVED
        )
    
    with col4:
        display_metric_card(
            "Başarı Oranı",
            f"%{stats['success_rate']}",
            "🎯",
            AppColors.INFO
        )
    
    st.markdown("---")
    
    # Recent reports
    st.markdown("### 📄 Son İhbarlarım")
    
    recent_reports = get_user_reports(user.id)[-3:]  # Last 3 reports
    
    if recent_reports:
        for report in reversed(recent_reports):  # Show newest first
            config = VIOLATION_CONFIG[report.violation_type]
            
            with st.expander(f"{config['icon']} {config['name_tr']} - #{report.id}"):
                col_info, col_status = st.columns([3, 1])
                
                with col_info:
                    st.write(f"**Açıklama:** {report.description}")
                    st.write(f"**Konum:** {report.location.address}")
                    st.write(f"**Tarih:** {report.created_at.strftime('%d.%m.%Y %H:%M')}")
                
                with col_status:
                    status_config = {
                        'pending': {'color': AppColors.PENDING, 'text': 'Beklemede', 'icon': '⏳'},
                        'in_review': {'color': AppColors.IN_REVIEW, 'text': 'İnceleniyor', 'icon': '🔍'},
                        'approved': {'color': AppColors.APPROVED, 'text': 'Onaylandı', 'icon': '✅'},
                        'rejected': {'color': AppColors.REJECTED, 'text': 'Reddedildi', 'icon': '❌'}
                    }
                    
                    status_info = status_config[report.status.value]
                    
                    st.markdown(f"""
                    <div style="text-align: center; padding: 0.5rem; background-color: {status_info['color']}20; border-radius: 8px; border: 1px solid {status_info['color']};">
                        <div style="font-size: 1.5rem;">{status_info['icon']}</div>
                        <div style="font-weight: 500; color: {status_info['color']};">{status_info['text']}</div>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    if report.reward_amount:
                        st.success(f"💰 ₺{report.reward_amount:.2f}")
        
        # View all reports button
        if st.button("📋 Tüm İhbarlarımı Gör", use_container_width=True):
            navigate_to('reports')
    
    else:
        display_info_message("""
        Henüz hiç ihbar oluşturmamışsınız. 
        İlk ihbarınızı oluşturmak için yukarıdaki "Şimdi İhbar Et" butonuna tıklayın!
        """)
    
    st.markdown("---")
    
    # Tips and information
    st.markdown("### 💡 İpuçları")
    
    tips = [
        {
            "icon": "📸",
            "title": "Kaliteli Fotoğraf Çekin",
            "description": "İhbarınızın onaylanması için net ve açık fotoğraflar çekin"
        },
        {
            "icon": "📍",
            "title": "Konum Bilgisi",
            "description": "Konumunuzun doğru olduğundan emin olun"
        },
        {
            "icon": "📝",
            "title": "Detaylı Açıklama",
            "description": "İhlali detaylı bir şekilde açıklayın"
        },
        {
            "icon": "⚡",
            "title": "Hızlı İşlem",
            "description": "İhbarlarınız 24-48 saat içinde değerlendirilir"
        }
    ]
    
    for i in range(0, len(tips), 2):
        col_tip1, col_tip2 = st.columns(2)
        
        with col_tip1:
            if i < len(tips):
                tip = tips[i]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">{tip['icon']}</div>
                    <div style="font-weight: 500; color: {AppColors.PRIMARY}; margin-bottom: 0.25rem;">{tip['title']}</div>
                    <div style="font-size: 0.875rem; color: #666;">{tip['description']}</div>
                </div>
                """, unsafe_allow_html=True)
        
        with col_tip2:
            if i + 1 < len(tips):
                tip = tips[i + 1]
                st.markdown(f"""
                <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem;">
                    <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">{tip['icon']}</div>
                    <div style="font-weight: 500; color: {AppColors.PRIMARY}; margin-bottom: 0.25rem;">{tip['title']}</div>
                    <div style="font-size: 0.875rem; color: #666;">{tip['description']}</div>
                </div>
                """, unsafe_allow_html=True)
