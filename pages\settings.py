# -*- coding: utf-8 -*-
"""
Settings Page - صفحة الإعدادات
إعدادات التطبيق واللغة
"""

import streamlit as st
from config import AppColors
from language_manager import get_text, language_selector, apply_rtl_css, is_rtl
from utils import display_success_message, display_info_message

def show_settings_page():
    """عرض صفحة الإعدادات"""
    
    # تطبيق CSS للغات RTL
    apply_rtl_css()
    
    # العنوان الرئيسي
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">
            ⚙️ {get_text('settings', 'Ayarlar')}
        </h1>
    </div>
    """, unsafe_allow_html=True)
    
    # تخطيط الصفحة
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        # قسم اللغة
        st.markdown(f"""
        <div style="
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid {AppColors.PRIMARY};
        ">
            <h3 style="color: {AppColors.PRIMARY}; margin-bottom: 1rem; display: flex; align-items: center;">
                🌐 {get_text('language', 'Dil')}
            </h3>
        </div>
        """, unsafe_allow_html=True)
        
        # منتقي اللغة
        st.markdown(f"**{get_text('language', 'Dil')} / Language / اللغة:**")
        selected_language = language_selector()
        
        # معلومات اللغة
        if selected_language:
            display_info_message(get_text('settings_saved', 'Ayarlarınız kaydedildi.'))
        
        st.markdown("---")
        
        # قسم الإشعارات
        st.markdown(f"""
        <div style="
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid {AppColors.SECONDARY};
        ">
            <h3 style="color: {AppColors.SECONDARY}; margin-bottom: 1rem;">
                🔔 {get_text('notifications', 'Bildirimler')}
            </h3>
        </div>
        """, unsafe_allow_html=True)
        
        # إعدادات الإشعارات
        notifications_enabled = st.checkbox(
            get_text('notifications', 'Bildirimler'),
            value=True,
            help=get_text('notifications', 'Bildirimler')
        )
        
        email_notifications = st.checkbox(
            f"📧 {get_text('email', 'E-posta')} {get_text('notifications', 'Bildirimler')}",
            value=True,
            help="E-posta bildirimleri"
        )
        
        push_notifications = st.checkbox(
            f"📱 Push {get_text('notifications', 'Bildirimler')}",
            value=True,
            help="Push bildirimleri"
        )
        
        st.markdown("---")
        
        # قسم الخصوصية
        st.markdown(f"""
        <div style="
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid {AppColors.SUCCESS};
        ">
            <h3 style="color: {AppColors.SUCCESS}; margin-bottom: 1rem;">
                🔒 {get_text('privacy_policy', 'Gizlilik Politikası')}
            </h3>
        </div>
        """, unsafe_allow_html=True)
        
        # روابط الخصوصية
        col_privacy1, col_privacy2 = st.columns(2)
        
        with col_privacy1:
            if st.button(f"📋 {get_text('privacy_policy', 'Gizlilik Politikası')}", use_container_width=True):
                st.info("Gizlilik politikası sayfası açılacak...")
        
        with col_privacy2:
            if st.button(f"📜 {get_text('terms_of_service', 'Kullanım Şartları')}", use_container_width=True):
                st.info("Kullanım şartları sayfası açılacak...")
        
        st.markdown("---")
        
        # قسم الحساب
        st.markdown(f"""
        <div style="
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid {AppColors.WARNING};
        ">
            <h3 style="color: {AppColors.WARNING}; margin-bottom: 1rem;">
                👤 {get_text('profile', 'Profil')}
            </h3>
        </div>
        """, unsafe_allow_html=True)
        
        # أزرار الحساب
        col_account1, col_account2 = st.columns(2)
        
        with col_account1:
            if st.button(f"✏️ {get_text('edit', 'Düzenle')} {get_text('profile', 'Profil')}", use_container_width=True):
                st.info("Profil düzenleme sayfası açılacak...")
        
        with col_account2:
            if st.button(f"🚪 {get_text('logout', 'Çıkış Yap')}", use_container_width=True, type="secondary"):
                if 'authenticated' in st.session_state:
                    del st.session_state.authenticated
                display_success_message(get_text('logout_success', 'Başarıyla çıkış yaptınız.'))
                st.rerun()
        
        st.markdown("---")
        
        # قسم حول التطبيق
        st.markdown(f"""
        <div style="
            background: white; 
            padding: 1.5rem; 
            border-radius: 12px; 
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 1.5rem;
            border-left: 4px solid {AppColors.INFO};
        ">
            <h3 style="color: {AppColors.INFO}; margin-bottom: 1rem;">
                ℹ️ {get_text('about', 'Hakkında')}
            </h3>
            <p style="color: #666; margin-bottom: 0.5rem;">
                <strong>{get_text('app_name', 'Mürşid')}</strong> - İhlal Bildirme Uygulaması
            </p>
            <p style="color: #666; margin-bottom: 0.5rem;">
                📱 Sürüm: 1.0.0
            </p>
            <p style="color: #666; margin-bottom: 0;">
                🏢 Geliştirici: Mürşid Team
            </p>
        </div>
        """, unsafe_allow_html=True)
        
        # زر حفظ الإعدادات
        if st.button(f"💾 {get_text('save', 'Kaydet')} {get_text('settings', 'Ayarlar')}", 
                    use_container_width=True, type="primary"):
            # حفظ الإعدادات
            st.session_state.notifications_enabled = notifications_enabled
            st.session_state.email_notifications = email_notifications
            st.session_state.push_notifications = push_notifications
            
            display_success_message(get_text('settings_saved', 'Ayarlarınız kaydedildi.'))

if __name__ == "__main__":
    show_settings_page()
