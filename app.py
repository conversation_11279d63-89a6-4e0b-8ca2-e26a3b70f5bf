# -*- coding: utf-8 -*-
"""
مرشد - تطبيق الإبلاغ عن المخالفات
تطبيق نافذة منفصلة بسيط وفعال
"""

import streamlit as st
import sqlite3
import hashlib
from datetime import datetime
import os

# إعدادات الصفحة للهاتف المحمول
st.set_page_config(
    page_title="مرشد - تطبيق الإبلاغ عن المخالفات",
    page_icon="🛡️",
    layout="centered",
    initial_sidebar_state="collapsed"  # إخفاء الشريط الجانبي للهواتف
)

# الألوان والتصميم
PRIMARY_COLOR = "#1f77b4"
SUCCESS_COLOR = "#28a745"
ERROR_COLOR = "#dc3545"
WARNING_COLOR = "#ffc107"

# CSS مخصص للهواتف المحمولة
st.markdown(f"""
<style>
    /* إعدادات عامة للهاتف */
    .main .block-container {{
        padding-top: 1rem;
        padding-bottom: 1rem;
        padding-left: 1rem;
        padding-right: 1rem;
        max-width: 100%;
    }}

    /* العنوان الرئيسي */
    .main-header {{
        text-align: center;
        color: {PRIMARY_COLOR};
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        padding: 0.5rem;
    }}

    /* رسائل النجاح */
    .success-message {{
        background-color: {SUCCESS_COLOR}20;
        color: {SUCCESS_COLOR};
        padding: 0.75rem;
        border-radius: 8px;
        border-left: 4px solid {SUCCESS_COLOR};
        margin: 0.5rem 0;
        font-size: 0.9rem;
    }}

    /* رسائل الخطأ */
    .error-message {{
        background-color: {ERROR_COLOR}20;
        color: {ERROR_COLOR};
        padding: 0.75rem;
        border-radius: 8px;
        border-left: 4px solid {ERROR_COLOR};
        margin: 0.5rem 0;
        font-size: 0.9rem;
    }}

    /* بطاقات المعلومات */
    .info-card {{
        background: white;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }}

    /* شارة الأدمن */
    .admin-badge {{
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 0.4rem 0.8rem;
        border-radius: 15px;
        font-weight: bold;
        display: inline-block;
        margin: 0.3rem 0;
        font-size: 0.8rem;
    }}

    /* تحسين الأزرار للهاتف */
    .stButton > button {{
        width: 100%;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        margin: 0.2rem 0;
    }}

    /* تحسين حقول الإدخال */
    .stTextInput > div > div > input {{
        border-radius: 8px;
        padding: 0.5rem;
        font-size: 0.9rem;
    }}

    /* تحسين القوائم المنسدلة */
    .stSelectbox > div > div > select {{
        border-radius: 8px;
        padding: 0.5rem;
        font-size: 0.9rem;
    }}

    /* إخفاء عناصر غير ضرورية */
    .stDeployButton {{
        display: none;
    }}

    /* تحسين الشريط الجانبي للهاتف */
    .css-1d391kg {{
        padding-top: 1rem;
    }}

    /* تحسين المقاييس للهاتف */
    .metric-container {{
        background: white;
        padding: 0.75rem;
        border-radius: 8px;
        box-shadow: 0 1px 4px rgba(0,0,0,0.1);
        margin: 0.25rem 0;
        text-align: center;
    }}

    /* تحسين التبويبات */
    .stTabs [data-baseweb="tab-list"] {{
        gap: 0.5rem;
    }}

    .stTabs [data-baseweb="tab"] {{
        padding: 0.5rem 0.75rem;
        font-size: 0.8rem;
    }}
</style>
""", unsafe_allow_html=True)

# قاعدة البيانات
def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect('mursid.db')
    cursor = conn.cursor()

    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            phone TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # إنشاء حساب الأدمن الافتراضي
    admin_password = hashlib.sha256("admin123".encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
        VALUES (?, ?, ?, ?)
    ''', ("<EMAIL>", admin_password, "System Administrator", True))

    conn.commit()
    conn.close()

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def authenticate_user(email, password):
    """التحقق من المستخدم"""
    conn = sqlite3.connect('mursid.db')
    cursor = conn.cursor()

    hashed_password = hash_password(password)
    cursor.execute('''
        SELECT id, email, full_name, is_admin
        FROM users
        WHERE email = ? AND password = ?
    ''', (email, hashed_password))

    user = cursor.fetchone()
    conn.close()

    if user:
        return {
            'id': user[0],
            'email': user[1],
            'full_name': user[2],
            'is_admin': bool(user[3])
        }
    return None

def register_user(email, password, full_name, phone=None):
    """تسجيل مستخدم جديد"""
    try:
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        hashed_password = hash_password(password)
        cursor.execute('''
            INSERT INTO users (email, password, full_name, phone)
            VALUES (?, ?, ?, ?)
        ''', (email, hashed_password, full_name, phone))

        conn.commit()
        conn.close()
        return True, "تم التسجيل بنجاح!"

    except sqlite3.IntegrityError:
        return False, "هذا البريد الإلكتروني مستخدم بالفعل!"
    except Exception as e:
        return False, f"خطأ في التسجيل: {str(e)}"

def show_success(message):
    """عرض رسالة نجاح"""
    st.markdown(f'<div class="success-message">✅ {message}</div>', unsafe_allow_html=True)

def show_error(message):
    """عرض رسالة خطأ"""
    st.markdown(f'<div class="error-message">❌ {message}</div>', unsafe_allow_html=True)

def show_login_page():
    """صفحة تسجيل الدخول للهاتف"""
    st.markdown('<div class="main-header">🛡️ مرشد - تطبيق الإبلاغ عن المخالفات</div>', unsafe_allow_html=True)

    # تخطيط مناسب للهاتف - عمود واحد
    st.markdown('<div class="info-card">', unsafe_allow_html=True)

    st.markdown("### 🔐 تسجيل الدخول")

    with st.form("login_form"):
        email = st.text_input("📧 البريد الإلكتروني", placeholder="<EMAIL>")
        password = st.text_input("🔒 كلمة المرور", type="password", placeholder="admin123")

        # أزرار بتخطيط هاتف
        login_button = st.form_submit_button("🚀 دخول", use_container_width=True, type="primary")
        register_button = st.form_submit_button("📝 تسجيل جديد", use_container_width=True)

        if login_button:
            if email and password:
                user = authenticate_user(email, password)
                if user:
                    st.session_state.user = user
                    st.session_state.logged_in = True
                    show_success("تم تسجيل الدخول بنجاح!")
                    st.rerun()
                else:
                    show_error("بيانات خاطئة!")
            else:
                show_error("يرجى ملء جميع الحقول!")

        if register_button:
            st.session_state.show_register = True
            st.rerun()

    st.markdown('</div>', unsafe_allow_html=True)

    # معلومات تجريبية
    st.info("""
    **معلومات تجريبية:**

    👑 **حساب الأدمن:**
    - البريد: <EMAIL>
    - كلمة المرور: admin123

    📝 **أو أنشئ حساب جديد**
    """)

def show_register_page():
    """صفحة التسجيل للهاتف"""
    st.markdown('<div class="main-header">📝 تسجيل حساب جديد</div>', unsafe_allow_html=True)

    # تخطيط مناسب للهاتف - عمود واحد
    st.markdown('<div class="info-card">', unsafe_allow_html=True)

    with st.form("register_form"):
        full_name = st.text_input("👤 الاسم الكامل", placeholder="أحمد محمد")
        email = st.text_input("📧 البريد الإلكتروني", placeholder="<EMAIL>")
        phone = st.text_input("📱 رقم الهاتف", placeholder="+90 ************")
        password = st.text_input("🔒 كلمة المرور", type="password", placeholder="6 أحرف على الأقل")
        confirm_password = st.text_input("🔒 تأكيد كلمة المرور", type="password")

        # أزرار بتخطيط هاتف
        register_button = st.form_submit_button("✅ تسجيل", use_container_width=True, type="primary")
        back_button = st.form_submit_button("🔙 رجوع", use_container_width=True)

        if register_button:
            if not all([full_name, email, password, confirm_password]):
                show_error("يرجى ملء جميع الحقول المطلوبة!")
            elif len(password) < 6:
                show_error("كلمة المرور يجب أن تكون 6 أحرف على الأقل!")
            elif password != confirm_password:
                show_error("كلمات المرور غير متطابقة!")
            else:
                success, message = register_user(email, password, full_name, phone)
                if success:
                    show_success(message)
                    st.session_state.show_register = False
                    st.rerun()
                else:
                    show_error(message)

        if back_button:
            st.session_state.show_register = False
            st.rerun()

    st.markdown('</div>', unsafe_allow_html=True)

def show_main_app():
    """التطبيق الرئيسي"""
    user = st.session_state.user

    # الشريط الجانبي
    with st.sidebar:
        st.markdown(f"### 👋 مرحباً {user['full_name']}")

        if user['is_admin']:
            st.markdown('<div class="admin-badge">👑 مدير النظام</div>', unsafe_allow_html=True)

        st.markdown("---")

        # القائمة
        menu_options = ["🏠 الرئيسية", "📝 بلاغ جديد", "📋 بلاغاتي", "💰 المحفظة", "👤 الملف الشخصي"]

        if user['is_admin']:
            menu_options.insert(0, "👑 لوحة التحكم")

        selected = st.selectbox("📱 القائمة", menu_options)

        st.markdown("---")

        if st.button("🚪 تسجيل الخروج", use_container_width=True):
            st.session_state.logged_in = False
            st.session_state.user = None
            st.rerun()

    # المحتوى الرئيسي
    if selected == "👑 لوحة التحكم":
        show_admin_dashboard()
    elif selected == "🏠 الرئيسية":
        show_home_page()
    elif selected == "📝 بلاغ جديد":
        show_new_report_page()
    elif selected == "📋 بلاغاتي":
        show_reports_page()
    elif selected == "💰 المحفظة":
        show_wallet_page()
    elif selected == "👤 الملف الشخصي":
        show_profile_page()

def show_admin_dashboard():
    """لوحة تحكم الأدمن"""
    st.markdown('<div class="main-header">👑 لوحة تحكم المدير</div>', unsafe_allow_html=True)

    # إحصائيات
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("👥 المستخدمين", "1,247", "12")

    with col2:
        st.metric("📋 البلاغات", "3,456", "89")

    with col3:
        st.metric("✅ المقبولة", "2,890", "45")

    with col4:
        st.metric("💰 المكافآت", "₺45,678", "₺1,234")

    st.markdown("---")

    # تبويبات الإدارة
    tab1, tab2, tab3 = st.tabs(["📊 الإحصائيات", "👥 المستخدمين", "⚙️ الإعدادات"])

    with tab1:
        st.info("📈 هنا ستظهر الرسوم البيانية والإحصائيات المفصلة")

    with tab2:
        st.info("👥 هنا ستظهر قائمة المستخدمين وإدارتهم")

    with tab3:
        st.info("⚙️ هنا ستظهر إعدادات النظام")

def show_home_page():
    """الصفحة الرئيسية"""
    st.markdown('<div class="main-header">🏠 الصفحة الرئيسية</div>', unsafe_allow_html=True)

    st.success("🎉 مرحباً بك في تطبيق مرشد!")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        ### 📝 إنشاء بلاغ جديد
        أبلغ عن المخالفات بسهولة
        """)
        if st.button("📝 بلاغ جديد", use_container_width=True, type="primary"):
            st.info("سيتم تطوير هذه الميزة قريباً")

    with col2:
        st.markdown("""
        ### 📋 بلاغاتي
        تابع حالة بلاغاتك
        """)
        if st.button("📋 عرض البلاغات", use_container_width=True):
            st.info("سيتم تطوير هذه الميزة قريباً")

def show_new_report_page():
    """صفحة بلاغ جديد"""
    st.markdown('<div class="main-header">📝 بلاغ جديد</div>', unsafe_allow_html=True)
    st.info("🚧 هذه الميزة قيد التطوير")

def show_reports_page():
    """صفحة البلاغات"""
    st.markdown('<div class="main-header">📋 بلاغاتي</div>', unsafe_allow_html=True)
    st.info("🚧 هذه الميزة قيد التطوير")

def show_wallet_page():
    """صفحة المحفظة"""
    st.markdown('<div class="main-header">💰 المحفظة</div>', unsafe_allow_html=True)
    st.info("🚧 هذه الميزة قيد التطوير")

def show_profile_page():
    """صفحة الملف الشخصي"""
    st.markdown('<div class="main-header">👤 الملف الشخصي</div>', unsafe_allow_html=True)
    st.info("🚧 هذه الميزة قيد التطوير")

# التطبيق الرئيسي
def main():
    """الدالة الرئيسية"""
    # إنشاء قاعدة البيانات
    init_database()

    # تهيئة حالة الجلسة
    if 'logged_in' not in st.session_state:
        st.session_state.logged_in = False
    if 'show_register' not in st.session_state:
        st.session_state.show_register = False

    # عرض الصفحة المناسبة
    if not st.session_state.logged_in:
        if st.session_state.get('show_register', False):
            show_register_page()
        else:
            show_login_page()
    else:
        show_main_app()

if __name__ == "__main__":
    main()