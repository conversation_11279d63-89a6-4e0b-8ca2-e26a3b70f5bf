# -*- coding: utf-8 -*-
"""
مرشد - تطبيق الإبلاغ عن المخالفات
تطبيق نافذة منفصلة بسيط وفعال
"""

import streamlit as st
import sqlite3
import hashlib
from datetime import datetime
import os

# إعدادات الصفحة
st.set_page_config(
    page_title="مرشد - تطبيق الإبلاغ عن المخالفات",
    page_icon="🛡️",
    layout="centered",
    initial_sidebar_state="expanded"
)

# الألوان والتصميم
PRIMARY_COLOR = "#1f77b4"
SUCCESS_COLOR = "#28a745"
ERROR_COLOR = "#dc3545"
WARNING_COLOR = "#ffc107"

# CSS مخصص
st.markdown(f"""
<style>
    .main-header {{
        text-align: center;
        color: {PRIMARY_COLOR};
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 1rem;
    }}
    
    .success-message {{
        background-color: {SUCCESS_COLOR}20;
        color: {SUCCESS_COLOR};
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid {SUCCESS_COLOR};
        margin: 1rem 0;
    }}
    
    .error-message {{
        background-color: {ERROR_COLOR}20;
        color: {ERROR_COLOR};
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid {ERROR_COLOR};
        margin: 1rem 0;
    }}
    
    .info-card {{
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin: 1rem 0;
    }}
    
    .admin-badge {{
        background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 20px;
        font-weight: bold;
        display: inline-block;
        margin: 0.5rem 0;
    }}
</style>
""", unsafe_allow_html=True)

# قاعدة البيانات
def init_database():
    """إنشاء قاعدة البيانات والجداول"""
    conn = sqlite3.connect('mursid.db')
    cursor = conn.cursor()
    
    # جدول المستخدمين
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            full_name TEXT NOT NULL,
            phone TEXT,
            is_admin BOOLEAN DEFAULT FALSE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # إنشاء حساب الأدمن الافتراضي
    admin_password = hashlib.sha256("admin123".encode()).hexdigest()
    cursor.execute('''
        INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
        VALUES (?, ?, ?, ?)
    ''', ("<EMAIL>", admin_password, "System Administrator", True))
    
    conn.commit()
    conn.close()

def hash_password(password):
    """تشفير كلمة المرور"""
    return hashlib.sha256(password.encode()).hexdigest()

def authenticate_user(email, password):
    """التحقق من المستخدم"""
    conn = sqlite3.connect('mursid.db')
    cursor = conn.cursor()
    
    hashed_password = hash_password(password)
    cursor.execute('''
        SELECT id, email, full_name, is_admin 
        FROM users 
        WHERE email = ? AND password = ?
    ''', (email, hashed_password))
    
    user = cursor.fetchone()
    conn.close()
    
    if user:
        return {
            'id': user[0],
            'email': user[1],
            'full_name': user[2],
            'is_admin': bool(user[3])
        }
    return None

def register_user(email, password, full_name, phone=None):
    """تسجيل مستخدم جديد"""
    try:
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()
        
        hashed_password = hash_password(password)
        cursor.execute('''
            INSERT INTO users (email, password, full_name, phone)
            VALUES (?, ?, ?, ?)
        ''', (email, hashed_password, full_name, phone))
        
        conn.commit()
        conn.close()
        return True, "تم التسجيل بنجاح!"
        
    except sqlite3.IntegrityError:
        return False, "هذا البريد الإلكتروني مستخدم بالفعل!"
    except Exception as e:
        return False, f"خطأ في التسجيل: {str(e)}"

def show_success(message):
    """عرض رسالة نجاح"""
    st.markdown(f'<div class="success-message">✅ {message}</div>', unsafe_allow_html=True)

def show_error(message):
    """عرض رسالة خطأ"""
    st.markdown(f'<div class="error-message">❌ {message}</div>', unsafe_allow_html=True)

def show_login_page():
    """صفحة تسجيل الدخول"""
    st.markdown('<div class="main-header">🛡️ مرشد - تطبيق الإبلاغ عن المخالفات</div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown('<div class="info-card">', unsafe_allow_html=True)
        
        st.markdown("### 🔐 تسجيل الدخول")
        
        with st.form("login_form"):
            email = st.text_input("📧 البريد الإلكتروني", placeholder="<EMAIL>")
            password = st.text_input("🔒 كلمة المرور", type="password", placeholder="admin123")
            
            col_login, col_register = st.columns(2)
            
            with col_login:
                login_button = st.form_submit_button("🚀 دخول", use_container_width=True, type="primary")
            
            with col_register:
                register_button = st.form_submit_button("📝 تسجيل جديد", use_container_width=True)
            
            if login_button:
                if email and password:
                    user = authenticate_user(email, password)
                    if user:
                        st.session_state.user = user
                        st.session_state.logged_in = True
                        show_success("تم تسجيل الدخول بنجاح!")
                        st.rerun()
                    else:
                        show_error("بيانات خاطئة!")
                else:
                    show_error("يرجى ملء جميع الحقول!")
            
            if register_button:
                st.session_state.show_register = True
                st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
        
        # معلومات تجريبية
        st.info("""
        **معلومات تجريبية:**
        
        👑 **حساب الأدمن:**
        - البريد: <EMAIL>
        - كلمة المرور: admin123
        
        📝 **أو أنشئ حساب جديد**
        """)

def show_register_page():
    """صفحة التسجيل"""
    st.markdown('<div class="main-header">📝 تسجيل حساب جديد</div>', unsafe_allow_html=True)
    
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown('<div class="info-card">', unsafe_allow_html=True)
        
        with st.form("register_form"):
            full_name = st.text_input("👤 الاسم الكامل", placeholder="أحمد محمد")
            email = st.text_input("📧 البريد الإلكتروني", placeholder="<EMAIL>")
            phone = st.text_input("📱 رقم الهاتف", placeholder="+90 ************")
            password = st.text_input("🔒 كلمة المرور", type="password", placeholder="6 أحرف على الأقل")
            confirm_password = st.text_input("🔒 تأكيد كلمة المرور", type="password")
            
            col_register, col_back = st.columns(2)
            
            with col_register:
                register_button = st.form_submit_button("✅ تسجيل", use_container_width=True, type="primary")
            
            with col_back:
                back_button = st.form_submit_button("🔙 رجوع", use_container_width=True)
            
            if register_button:
                if not all([full_name, email, password, confirm_password]):
                    show_error("يرجى ملء جميع الحقول المطلوبة!")
                elif len(password) < 6:
                    show_error("كلمة المرور يجب أن تكون 6 أحرف على الأقل!")
                elif password != confirm_password:
                    show_error("كلمات المرور غير متطابقة!")
                else:
                    success, message = register_user(email, password, full_name, phone)
                    if success:
                        show_success(message)
                        st.session_state.show_register = False
                        st.rerun()
                    else:
                        show_error(message)
            
            if back_button:
                st.session_state.show_register = False
                st.rerun()
        
        st.markdown('</div>', unsafe_allow_html=True)
