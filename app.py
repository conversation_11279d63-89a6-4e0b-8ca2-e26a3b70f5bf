# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Main Application
"""

import streamlit as st
from streamlit_option_menu import option_menu

# Import configurations and utilities
from config import PAGE_CONFIG, get_custom_css, AppColors, APP_NAME
from utils import init_session_state, get_current_page
from language_manager import get_text, apply_rtl_css, is_rtl

# Import pages
from pages.login import show_login_page
from pages.home import show_home_page
from pages.new_report import show_new_report_page
from pages.reports import show_reports_page
from pages.wallet import show_wallet_page
from pages.profile import show_profile_page
from pages.settings import show_settings_page
from pages.admin_dashboard import show_admin_dashboard

def main():
    """Main application function"""

    # Configure page
    st.set_page_config(**PAGE_CONFIG)

    # Apply custom CSS
    st.markdown(get_custom_css(), unsafe_allow_html=True)

    # Initialize session state
    init_session_state()

    # Check authentication status
    if not st.session_state.authenticated:
        show_login_page()
        return

    # Main app layout for authenticated users
    show_main_app()

def show_main_app():
    """Show main application with navigation"""

    user = st.session_state.current_user
    current_page = get_current_page()

    # Sidebar navigation
    with st.sidebar:
        # User info in sidebar
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem; background: linear-gradient(135deg, {AppColors.PRIMARY}, {AppColors.PRIMARY_LIGHT}); border-radius: 12px; margin-bottom: 1rem; color: white;">
            <div style="font-size: 2rem; margin-bottom: 0.5rem;">👤</div>
            <div style="font-weight: bold; margin-bottom: 0.25rem;">{user.full_name}</div>
            <div style="font-size: 0.875rem; opacity: 0.9;">{user.email}</div>
        </div>
        """, unsafe_allow_html=True)

        # Navigation menu - مع إضافة لوحة تحكم الأدمن
        menu_options = [
            f"🏠 {get_text('home', 'Ana Sayfa')}",
            f"📝 {get_text('new_report', 'Yeni İhbar')}",
            f"📋 {get_text('reports', 'İhbarlarım')}",
            f"💰 {get_text('wallet', 'Cüzdan')}",
            f"👤 {get_text('profile', 'Profil')}",
            f"⚙️ {get_text('settings', 'Ayarlar')}"
        ]

        menu_icons = ["house", "plus-circle", "list-task", "wallet", "person", "gear"]

        # إضافة لوحة تحكم الأدمن للمدير فقط
        if hasattr(user, 'is_admin') and user.is_admin:
            menu_options.insert(0, "👑 لوحة التحكم")
            menu_icons.insert(0, "shield-check")

        selected = option_menu(
            menu_title=f"📱 {get_text('menu', 'Menü')}",
            options=menu_options,
            icons=menu_icons,
            menu_icon="app-indicator",
            default_index=get_menu_index(current_page),
            styles={
                "container": {"padding": "0!important", "background-color": "transparent"},
                "icon": {"color": AppColors.PRIMARY, "font-size": "18px"},
                "nav-link": {
                    "font-size": "16px",
                    "text-align": "left",
                    "margin": "0px",
                    "padding": "10px 15px",
                    "--hover-color": f"{AppColors.PRIMARY}20",
                },
                "nav-link-selected": {
                    "background-color": AppColors.PRIMARY,
                    "color": "white",
                    "border-radius": "8px",
                },
            }
        )

        # Handle navigation
        if selected == "👑 لوحة التحكم":
            st.session_state.current_page = 'admin_dashboard'
        elif selected == f"🏠 {get_text('home', 'Ana Sayfa')}":
            st.session_state.current_page = 'home'
        elif selected == f"📝 {get_text('new_report', 'Yeni İhbar')}":
            st.session_state.current_page = 'new_report'
        elif selected == f"📋 {get_text('reports', 'İhbarlarım')}":
            st.session_state.current_page = 'reports'
        elif selected == f"💰 {get_text('wallet', 'Cüzdan')}":
            st.session_state.current_page = 'wallet'
        elif selected == f"👤 {get_text('profile', 'Profil')}":
            st.session_state.current_page = 'profile'
        elif selected == f"⚙️ {get_text('settings', 'Ayarlar')}":
            st.session_state.current_page = 'settings'

        # Quick stats in sidebar
        st.markdown("---")
        st.markdown("### 📊 Hızlı Bilgiler")

        # Demo stats
        st.metric("Toplam İhbar", "12", "2")
        st.metric("Kazanç", "₺1,250", "₺200")
        st.metric("Başarı Oranı", "%85", "5%")

        # Quick actions
        st.markdown("---")
        st.markdown("### ⚡ Hızlı İşlemler")

        if st.button("🚀 Hızlı İhbar", use_container_width=True, type="primary"):
            st.session_state.current_page = 'new_report'
            st.rerun()

        if st.button("💰 Para Çek", use_container_width=True):
            st.session_state.current_page = 'wallet'
            st.rerun()

        # App info
        st.markdown("---")
        st.markdown(f"""
        <div style="text-align: center; padding: 1rem; background-color: #f8f9fa; border-radius: 8px; font-size: 0.875rem; color: #666;">
            <div style="font-size: 1.5rem; margin-bottom: 0.5rem;">🛡️</div>
            <strong>{APP_NAME}</strong><br>
            Vatandaş İhbar Uygulaması<br>
            <small>v1.0.0</small>
        </div>
        """, unsafe_allow_html=True)

    # Apply RTL CSS if needed
    apply_rtl_css()

    # Main content area
    current_page = get_current_page()

    if current_page == 'admin_dashboard':
        show_admin_dashboard()
    elif current_page == 'home':
        show_home_page()
    elif current_page == 'new_report':
        show_new_report_page()
    elif current_page == 'reports':
        show_reports_page()
    elif current_page == 'wallet':
        show_wallet_page()
    elif current_page == 'profile':
        show_profile_page()
    elif current_page == 'settings':
        show_settings_page()
    else:
        show_home_page()

def get_menu_index(current_page: str) -> int:
    """Get menu index based on current page"""
    # Check if current user is admin
    user = st.session_state.get('current_user')
    is_admin = hasattr(user, 'is_admin') and user.is_admin if user else False

    if is_admin:
        # Admin menu includes admin dashboard at index 0
        page_to_index = {
            'admin_dashboard': 0,
            'home': 1,
            'new_report': 2,
            'reports': 3,
            'wallet': 4,
            'profile': 5,
            'settings': 6
        }
    else:
        # Regular user menu
        page_to_index = {
            'home': 0,
            'new_report': 1,
            'reports': 2,
            'wallet': 3,
            'profile': 4,
            'settings': 5
        }

    return page_to_index.get(current_page, 0)

# Custom components
def show_notification_toast(message: str, type: str = "info"):
    """Show notification toast"""
    colors = {
        "success": AppColors.SUCCESS,
        "error": AppColors.ERROR,
        "warning": AppColors.WARNING,
        "info": AppColors.INFO
    }

    icons = {
        "success": "✅",
        "error": "❌",
        "warning": "⚠️",
        "info": "ℹ️"
    }

    color = colors.get(type, AppColors.INFO)
    icon = icons.get(type, "ℹ️")

    st.markdown(f"""
    <div style="
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: {color};
        color: white;
        padding: 1rem 1.5rem;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    ">
        {icon} {message}
    </div>

    <style>
    @keyframes slideIn {{
        from {{
            transform: translateX(100%);
            opacity: 0;
        }}
        to {{
            transform: translateX(0);
            opacity: 1;
        }}
    }}
    </style>
    """, unsafe_allow_html=True)

def show_loading_spinner():
    """Show loading spinner"""
    st.markdown(f"""
    <div style="text-align: center; padding: 2rem;">
        <div style="
            width: 40px;
            height: 40px;
            border: 4px solid {AppColors.PRIMARY}20;
            border-top: 4px solid {AppColors.PRIMARY};
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem auto;
        "></div>
        <p style="color: #666;">Yükleniyor...</p>
    </div>

    <style>
    @keyframes spin {{
        0% {{ transform: rotate(0deg); }}
        100% {{ transform: rotate(360deg); }}
    }}
    </style>
    """, unsafe_allow_html=True)

def show_empty_state(title: str, description: str, action_text: str = None, action_callback = None):
    """Show empty state component"""
    st.markdown(f"""
    <div style="text-align: center; padding: 3rem 1rem; background-color: #f8f9fa; border-radius: 12px;">
        <div style="font-size: 4rem; margin-bottom: 1rem; opacity: 0.5;">📭</div>
        <h3 style="color: #666; margin-bottom: 1rem;">{title}</h3>
        <p style="color: #999; margin-bottom: 2rem; max-width: 400px; margin-left: auto; margin-right: auto;">
            {description}
        </p>
    </div>
    """, unsafe_allow_html=True)

    if action_text and action_callback:
        if st.button(action_text, use_container_width=True, type="primary"):
            action_callback()

# Error handling
def show_error_page(error_message: str):
    """Show error page"""
    st.markdown(f"""
    <div style="text-align: center; padding: 3rem 1rem;">
        <div style="font-size: 4rem; margin-bottom: 1rem;">😞</div>
        <h2 style="color: {AppColors.ERROR}; margin-bottom: 1rem;">Bir Hata Oluştu</h2>
        <p style="color: #666; margin-bottom: 2rem;">{error_message}</p>
    </div>
    """, unsafe_allow_html=True)

    col1, col2, col3 = st.columns([1, 1, 1])

    with col2:
        if st.button("🔄 Yeniden Dene", use_container_width=True, type="primary"):
            st.rerun()

# Run the app
if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        st.error(f"Uygulama başlatılırken hata oluştu: {str(e)}")
        show_error_page("Uygulama başlatılamadı. Lütfen sayfayı yenileyin.")
