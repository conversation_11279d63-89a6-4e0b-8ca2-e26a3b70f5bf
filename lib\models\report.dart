import 'package:json_annotation/json_annotation.dart';

part 'report.g.dart';

@JsonSerializable()
class Report {
  final String id;
  final String userId;
  final ViolationType violationType;
  final String description;
  final List<String> mediaUrls;
  final Location location;
  final ReportStatus status;
  final double? fineAmount;
  final double? rewardAmount;
  final String? officerNotes;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Report({
    required this.id,
    required this.userId,
    required this.violationType,
    required this.description,
    required this.mediaUrls,
    required this.location,
    required this.status,
    this.fineAmount,
    this.rewardAmount,
    this.officerNotes,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Report.fromJson(Map<String, dynamic> json) => _$ReportFromJson(json);
  Map<String, dynamic> toJson() => _$ReportToJson(this);

  Report copyWith({
    String? id,
    String? userId,
    ViolationType? violationType,
    String? description,
    List<String>? mediaUrls,
    Location? location,
    ReportStatus? status,
    double? fineAmount,
    double? rewardAmount,
    String? officerNotes,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Report(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      violationType: violationType ?? this.violationType,
      description: description ?? this.description,
      mediaUrls: mediaUrls ?? this.mediaUrls,
      location: location ?? this.location,
      status: status ?? this.status,
      fineAmount: fineAmount ?? this.fineAmount,
      rewardAmount: rewardAmount ?? this.rewardAmount,
      officerNotes: officerNotes ?? this.officerNotes,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class Location {
  final double latitude;
  final double longitude;
  final String address;

  const Location({
    required this.latitude,
    required this.longitude,
    required this.address,
  });

  factory Location.fromJson(Map<String, dynamic> json) => _$LocationFromJson(json);
  Map<String, dynamic> toJson() => _$LocationToJson(this);
}

enum ViolationType {
  @JsonValue('traffic')
  traffic,
  @JsonValue('cleanliness')
  cleanliness,
  @JsonValue('harassment')
  harassment,
  @JsonValue('violence')
  violence,
  @JsonValue('noise')
  noise,
  @JsonValue('parking')
  parking,
  @JsonValue('other')
  other,
}

enum ReportStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('in_review')
  inReview,
  @JsonValue('approved')
  approved,
  @JsonValue('rejected')
  rejected,
}
