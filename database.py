# -*- coding: utf-8 -*-
"""
Database Manager - مدير قاعدة البيانات
إدارة قاعدة بيانات SQLite للتطبيق
"""

import sqlite3
import hashlib
import os
from datetime import datetime
from typing import Optional, List, Dict, Any

class DatabaseManager:
    """مدير قاعدة البيانات"""

    def __init__(self, db_path: str = "mursid.db"):
        self.db_path = db_path
        self.init_database()

    def get_connection(self):
        """الحصول على اتصال قاعدة البيانات"""
        conn = sqlite3.connect(self.db_path, timeout=30.0)
        conn.row_factory = sqlite3.Row  # للحصول على النتائج كقاموس
        # تحسين الأداء
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL")
        conn.execute("PRAGMA cache_size=1000")
        conn.execute("PRAGMA temp_store=MEMORY")
        return conn

    def init_database(self):
        """إنشاء الجداول الأساسية"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            print("🔄 إنشاء قاعدة البيانات...")
            # جدول المستخدمين
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    email TEXT UNIQUE NOT NULL,
                    password_hash TEXT NOT NULL,
                    full_name TEXT NOT NULL,
                    phone TEXT,
                    is_verified BOOLEAN DEFAULT FALSE,
                    is_admin BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    last_login TIMESTAMP,
                    is_active BOOLEAN DEFAULT TRUE
                )
            """)

            # جدول البلاغات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS reports (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    violation_type TEXT NOT NULL,
                    description TEXT NOT NULL,
                    location_lat REAL,
                    location_lng REAL,
                    location_address TEXT,
                    status TEXT DEFAULT 'pending',
                    images TEXT,  -- JSON array of image paths
                    officer_notes TEXT,
                    reward_amount REAL DEFAULT 0,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    reviewed_at TIMESTAMP,
                    reviewed_by INTEGER,
                    FOREIGN KEY (user_id) REFERENCES users (id),
                    FOREIGN KEY (reviewed_by) REFERENCES users (id)
                )
            """)

            # جدول المعاملات المالية
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER NOT NULL,
                    type TEXT NOT NULL,  -- 'reward', 'withdrawal'
                    amount REAL NOT NULL,
                    description TEXT,
                    status TEXT DEFAULT 'pending',  -- 'pending', 'completed', 'failed'
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    processed_at TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            # جدول إعدادات النظام
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS system_settings (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    description TEXT,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # جدول سجل النشاطات
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS activity_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    user_id INTEGER,
                    action TEXT NOT NULL,
                    details TEXT,
                    ip_address TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)

            conn.commit()

            # إنشاء المستخدم الأدمن الافتراضي
            self.create_default_admin()

            # إنشاء الإعدادات الافتراضية
            self.create_default_settings()

            print("✅ تم إنشاء قاعدة البيانات بنجاح!")

        except Exception as e:
            print(f"❌ خطأ في إنشاء قاعدة البيانات: {e}")
            import traceback
            traceback.print_exc()
            if 'conn' in locals():
                conn.rollback()
        finally:
            if 'conn' in locals():
                conn.close()

    def hash_password(self, password: str) -> str:
        """تشفير كلمة المرور"""
        return hashlib.sha256(password.encode()).hexdigest()

    def verify_password(self, password: str, password_hash: str) -> bool:
        """التحقق من كلمة المرور"""
        return self.hash_password(password) == password_hash

    def create_default_admin(self):
        """إنشاء حساب الأدمن الافتراضي"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # التحقق من وجود الأدمن
            cursor.execute("SELECT id FROM users WHERE email = ?", ("<EMAIL>",))
            if cursor.fetchone():
                return  # الأدمن موجود بالفعل

            # إنشاء حساب الأدمن
            admin_data = {
                'email': '<EMAIL>',
                'password_hash': self.hash_password('admin123'),
                'full_name': 'System Administrator',
                'phone': '+90 ************',
                'is_verified': True,
                'is_admin': True,
                'is_active': True
            }

            cursor.execute("""
                INSERT INTO users (email, password_hash, full_name, phone, is_verified, is_admin, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """, (
                admin_data['email'],
                admin_data['password_hash'],
                admin_data['full_name'],
                admin_data['phone'],
                admin_data['is_verified'],
                admin_data['is_admin'],
                admin_data['is_active']
            ))

            conn.commit()
            print("✅ تم إنشاء حساب الأدمن الافتراضي")

        except Exception as e:
            print(f"خطأ في إنشاء حساب الأدمن: {e}")
            conn.rollback()
        finally:
            conn.close()

    def create_default_settings(self):
        """إنشاء الإعدادات الافتراضية"""
        conn = self.get_connection()
        cursor = conn.cursor()

        default_settings = [
            ('reward_percentage', '20', 'نسبة المكافأة من قيمة المخالفة'),
            ('max_reports_per_day', '10', 'الحد الأقصى للبلاغات يومياً لكل مستخدم'),
            ('min_withdrawal_amount', '50', 'الحد الأدنى لسحب الأموال'),
            ('auto_approve_reports', 'false', 'الموافقة التلقائية على البلاغات'),
            ('maintenance_mode', 'false', 'وضع الصيانة'),
            ('app_version', '1.0.0', 'إصدار التطبيق'),
        ]

        try:
            for key, value, description in default_settings:
                cursor.execute("""
                    INSERT OR IGNORE INTO system_settings (key, value, description)
                    VALUES (?, ?, ?)
                """, (key, value, description))

            conn.commit()
            print("✅ تم إنشاء الإعدادات الافتراضية")

        except Exception as e:
            print(f"خطأ في إنشاء الإعدادات: {e}")
            conn.rollback()
        finally:
            conn.close()

    def authenticate_user(self, email: str, password: str) -> Optional[Dict[str, Any]]:
        """مصادقة المستخدم"""
        try:
            print(f"🔍 محاولة تسجيل دخول: {email}")
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                SELECT id, email, password_hash, full_name, phone, is_verified, is_admin, is_active
                FROM users
                WHERE email = ? AND is_active = TRUE
            """, (email,))

            user_row = cursor.fetchone()

            if user_row and self.verify_password(password, user_row['password_hash']):
                # تحديث آخر تسجيل دخول
                cursor.execute("""
                    UPDATE users SET last_login = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (user_row['id'],))

                # تسجيل النشاط
                self.log_activity(user_row['id'], 'login', f'تسجيل دخول من {email}')

                conn.commit()

                # إرجاع بيانات المستخدم
                return {
                    'id': user_row['id'],
                    'email': user_row['email'],
                    'full_name': user_row['full_name'],
                    'phone': user_row['phone'],
                    'is_verified': bool(user_row['is_verified']),
                    'is_admin': bool(user_row['is_admin']),
                    'is_active': bool(user_row['is_active'])
                }

            print(f"❌ فشل تسجيل الدخول: {email}")
            return None

        except Exception as e:
            print(f"❌ خطأ في مصادقة المستخدم: {e}")
            import traceback
            traceback.print_exc()
            return None
        finally:
            if 'conn' in locals():
                conn.close()

    def create_user(self, email: str, password: str, full_name: str, phone: str = None) -> bool:
        """إنشاء مستخدم جديد"""
        try:
            print(f"🔄 إنشاء مستخدم جديد: {email}")
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO users (email, password_hash, full_name, phone)
                VALUES (?, ?, ?, ?)
            """, (email, self.hash_password(password), full_name, phone))

            user_id = cursor.lastrowid
            self.log_activity(user_id, 'register', f'تسجيل مستخدم جديد: {email}')

            conn.commit()
            print(f"✅ تم إنشاء المستخدم بنجاح: {email}")
            return True

        except sqlite3.IntegrityError:
            print(f"❌ البريد الإلكتروني {email} مستخدم بالفعل")
            return False
        except Exception as e:
            print(f"❌ خطأ في إنشاء المستخدم: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            if 'conn' in locals():
                conn.close()

    def log_activity(self, user_id: int, action: str, details: str = None, ip_address: str = None):
        """تسجيل نشاط المستخدم"""
        try:
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute("""
                INSERT INTO activity_log (user_id, action, details, ip_address)
                VALUES (?, ?, ?, ?)
            """, (user_id, action, details, ip_address))

            conn.commit()

        except Exception as e:
            # تجاهل أخطاء تسجيل النشاط لتجنب توقف العمليات الأساسية
            pass
        finally:
            if 'conn' in locals():
                conn.close()

    def get_user_stats(self) -> Dict[str, int]:
        """الحصول على إحصائيات المستخدمين"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # إجمالي المستخدمين
            cursor.execute("SELECT COUNT(*) as total FROM users WHERE is_active = TRUE")
            total_users = cursor.fetchone()['total']

            # المستخدمين النشطين اليوم
            cursor.execute("""
                SELECT COUNT(*) as active_today
                FROM users
                WHERE DATE(last_login) = DATE('now') AND is_active = TRUE
            """)
            active_today = cursor.fetchone()['active_today']

            # المستخدمين الجدد هذا الشهر
            cursor.execute("""
                SELECT COUNT(*) as new_this_month
                FROM users
                WHERE DATE(created_at) >= DATE('now', 'start of month') AND is_active = TRUE
            """)
            new_this_month = cursor.fetchone()['new_this_month']

            return {
                'total_users': total_users,
                'active_today': active_today,
                'new_this_month': new_this_month
            }

        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات المستخدمين: {e}")
            return {'total_users': 0, 'active_today': 0, 'new_this_month': 0}
        finally:
            conn.close()

# إنشاء مثيل عام لمدير قاعدة البيانات
db_manager = DatabaseManager()

def get_db_manager() -> DatabaseManager:
    """الحصول على مدير قاعدة البيانات"""
    return db_manager
