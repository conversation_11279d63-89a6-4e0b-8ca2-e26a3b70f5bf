#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مرشد - تطبيق جميل ومحسن
تطبيق نافذة GUI جميلة بتصميم محسن
"""

import tkinter as tk
from tkinter import ttk, messagebox, font
import sqlite3
import hashlib
from datetime import datetime
import os

class BeautifulMurshidApp:
    def __init__(self):
        self.root = tk.Tk()
        self.setup_window()
        self.setup_database()
        self.current_user = None
        self.setup_styles()
        self.show_login_page()

    def setup_window(self):
        """إعداد النافذة الرئيسية"""
        self.root.title("🛡️ مرشد - تطبيق الإبلاغ عن المخالفات")
        self.root.geometry("360x640")
        self.root.resizable(False, False)

        # توسيط النافذة
        self.root.update_idletasks()
        x = (self.root.winfo_screenwidth() // 2) - (360 // 2)
        y = (self.root.winfo_screenheight() // 2) - (640 // 2)
        self.root.geometry(f"360x640+{x}+{y}")

        # الألوان الجميلة
        self.colors = {
            'primary': '#4A90E2',
            'primary_dark': '#357ABD',
            'success': '#7ED321',
            'success_dark': '#5BA617',
            'error': '#D0021B',
            'warning': '#F5A623',
            'background': '#F8F9FA',
            'surface': '#FFFFFF',
            'text': '#2C3E50',
            'text_light': '#7F8C8D',
            'border': '#E1E8ED'
        }

        self.root.configure(bg=self.colors['background'])

    def setup_styles(self):
        """إعداد الأنماط الجميلة"""
        # خطوط جميلة
        self.fonts = {
            'title': font.Font(family="Segoe UI", size=18, weight="bold"),
            'header': font.Font(family="Segoe UI", size=14, weight="bold"),
            'body': font.Font(family="Segoe UI", size=11),
            'button': font.Font(family="Segoe UI", size=11, weight="bold"),
            'small': font.Font(family="Segoe UI", size=9)
        }

        # إعداد ttk styles
        self.style = ttk.Style()
        self.style.theme_use('clam')

        # تخصيص الأزرار
        self.style.configure('Primary.TButton',
                           background=self.colors['primary'],
                           foreground='white',
                           borderwidth=0,
                           focuscolor='none',
                           font=self.fonts['button'])

        self.style.map('Primary.TButton',
                      background=[('active', self.colors['primary_dark'])])

    def setup_database(self):
        """إعداد قاعدة البيانات"""
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                email TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                full_name TEXT NOT NULL,
                phone TEXT,
                is_admin BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        ''')

        admin_password = hashlib.sha256("admin123".encode()).hexdigest()
        cursor.execute('''
            INSERT OR IGNORE INTO users (email, password, full_name, is_admin)
            VALUES (?, ?, ?, ?)
        ''', ("<EMAIL>", admin_password, "System Administrator", True))

        conn.commit()
        conn.close()

    def hash_password(self, password):
        return hashlib.sha256(password.encode()).hexdigest()

    def authenticate_user(self, email, password):
        conn = sqlite3.connect('mursid.db')
        cursor = conn.cursor()

        hashed_password = self.hash_password(password)
        cursor.execute('''
            SELECT id, email, full_name, is_admin
            FROM users
            WHERE email = ? AND password = ?
        ''', (email, hashed_password))

        user = cursor.fetchone()
        conn.close()

        if user:
            return {
                'id': user[0],
                'email': user[1],
                'full_name': user[2],
                'is_admin': bool(user[3])
            }
        return None

    def register_user(self, email, password, full_name, phone=None):
        try:
            conn = sqlite3.connect('mursid.db')
            cursor = conn.cursor()

            hashed_password = self.hash_password(password)
            cursor.execute('''
                INSERT INTO users (email, password, full_name, phone)
                VALUES (?, ?, ?, ?)
            ''', (email, hashed_password, full_name, phone))

            conn.commit()
            conn.close()
            return True, "تم التسجيل بنجاح!"

        except sqlite3.IntegrityError:
            return False, "هذا البريد الإلكتروني مستخدم بالفعل!"
        except Exception as e:
            return False, f"خطأ في التسجيل: {str(e)}"

    def clear_window(self):
        for widget in self.root.winfo_children():
            widget.destroy()

    def create_card(self, parent, **kwargs):
        """إنشاء بطاقة جميلة"""
        card = tk.Frame(
            parent,
            bg=self.colors['surface'],
            relief=tk.FLAT,
            bd=0,
            **kwargs
        )

        # إضافة ظل بسيط
        shadow = tk.Frame(
            parent,
            bg=self.colors['border'],
            height=2
        )

        return card

    def create_button(self, parent, text, command, style='primary', **kwargs):
        """إنشاء زر جميل"""
        colors = {
            'primary': (self.colors['primary'], self.colors['primary_dark']),
            'success': (self.colors['success'], self.colors['success_dark']),
            'error': (self.colors['error'], '#B71C1C'),
            'warning': (self.colors['warning'], '#E65100')
        }

        bg_color, hover_color = colors.get(style, colors['primary'])

        btn = tk.Button(
            parent,
            text=text,
            command=command,
            bg=bg_color,
            fg='white',
            font=self.fonts['button'],
            relief=tk.FLAT,
            bd=0,
            cursor='hand2',
            activebackground=hover_color,
            activeforeground='white',
            **kwargs
        )

        # تأثير hover
        def on_enter(e):
            btn.configure(bg=hover_color)

        def on_leave(e):
            btn.configure(bg=bg_color)

        btn.bind("<Enter>", on_enter)
        btn.bind("<Leave>", on_leave)

        return btn

    def create_entry(self, parent, placeholder="", show="", **kwargs):
        """إنشاء حقل إدخال جميل"""
        entry = tk.Entry(
            parent,
            font=self.fonts['body'],
            bg=self.colors['surface'],
            fg=self.colors['text'],
            relief=tk.SOLID,
            bd=1,
            highlightthickness=2,
            highlightcolor=self.colors['primary'],
            highlightbackground=self.colors['border'],
            show=show,
            **kwargs
        )

        # إضافة placeholder
        if placeholder:
            entry.insert(0, placeholder)
            entry.configure(fg=self.colors['text_light'])

            def on_focus_in(event):
                if entry.get() == placeholder:
                    entry.delete(0, tk.END)
                    entry.configure(fg=self.colors['text'])

            def on_focus_out(event):
                if not entry.get():
                    entry.insert(0, placeholder)
                    entry.configure(fg=self.colors['text_light'])

            entry.bind('<FocusIn>', on_focus_in)
            entry.bind('<FocusOut>', on_focus_out)

        return entry

    def show_login_page(self):
        """صفحة تسجيل الدخول الجميلة"""
        self.clear_window()

        # الخلفية الرئيسية
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # العنوان الرئيسي
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(pady=(20, 30))

        # أيقونة كبيرة
        icon_label = tk.Label(
            title_frame,
            text="🛡️",
            font=font.Font(size=48),
            bg=self.colors['background'],
            fg=self.colors['primary']
        )
        icon_label.pack()

        # عنوان التطبيق
        title_label = tk.Label(
            title_frame,
            text="مرشد",
            font=self.fonts['title'],
            bg=self.colors['background'],
            fg=self.colors['primary']
        )
        title_label.pack(pady=(10, 5))

        # وصف التطبيق
        desc_label = tk.Label(
            title_frame,
            text="تطبيق الإبلاغ عن المخالفات",
            font=self.fonts['body'],
            bg=self.colors['background'],
            fg=self.colors['text_light']
        )
        desc_label.pack()

        # بطاقة تسجيل الدخول
        login_card = self.create_card(main_frame)
        login_card.pack(fill=tk.X, pady=20)

        # محتوى البطاقة
        card_content = tk.Frame(login_card, bg=self.colors['surface'])
        card_content.pack(fill=tk.BOTH, expand=True, padx=25, pady=25)

        # عنوان تسجيل الدخول
        login_title = tk.Label(
            card_content,
            text="🔐 تسجيل الدخول",
            font=self.fonts['header'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        )
        login_title.pack(pady=(0, 20))

        # حقل البريد الإلكتروني
        email_label = tk.Label(
            card_content,
            text="📧 البريد الإلكتروني",
            font=self.fonts['body'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        )
        email_label.pack(anchor=tk.W, pady=(0, 5))

        self.email_entry = self.create_entry(
            card_content,
            width=35
        )
        self.email_entry.pack(fill=tk.X, pady=(0, 15), ipady=8)
        self.email_entry.insert(0, "<EMAIL>")

        # حقل كلمة المرور
        password_label = tk.Label(
            card_content,
            text="🔒 كلمة المرور",
            font=self.fonts['body'],
            bg=self.colors['surface'],
            fg=self.colors['text']
        )
        password_label.pack(anchor=tk.W, pady=(0, 5))

        self.password_entry = self.create_entry(
            card_content,
            show="*",
            width=35
        )
        self.password_entry.pack(fill=tk.X, pady=(0, 20), ipady=8)
        self.password_entry.insert(0, "admin123")

        # أزرار
        login_btn = self.create_button(
            card_content,
            "🚀 دخول",
            self.login,
            style='primary'
        )
        login_btn.pack(fill=tk.X, pady=(0, 10), ipady=12)

        register_btn = self.create_button(
            card_content,
            "📝 تسجيل جديد",
            self.show_register_page,
            style='success'
        )
        register_btn.pack(fill=tk.X, ipady=10)

        # معلومات تجريبية
        info_frame = tk.Frame(main_frame, bg=self.colors['background'])
        info_frame.pack(pady=15)

        info_text = """💡 معلومات تجريبية:
👑 حساب الأدمن: <EMAIL> / admin123
📝 أو أنشئ حساب جديد"""

        info_label = tk.Label(
            info_frame,
            text=info_text,
            font=self.fonts['small'],
            bg=self.colors['background'],
            fg=self.colors['text_light'],
            justify=tk.CENTER
        )
        info_label.pack()

    def login(self):
        """تسجيل الدخول"""
        email = self.email_entry.get().strip()
        password = self.password_entry.get().strip()

        if not email or not password:
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول!")
            return

        user = self.authenticate_user(email, password)
        if user:
            self.current_user = user
            messagebox.showinfo("نجح", "تم تسجيل الدخول بنجاح!")
            self.show_main_page()
        else:
            messagebox.showerror("خطأ", "بيانات خاطئة!")

    def show_register_page(self):
        """صفحة التسجيل الجميلة"""
        self.clear_window()

        # الخلفية الرئيسية
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=15)

        # العنوان
        title_frame = tk.Frame(main_frame, bg=self.colors['background'])
        title_frame.pack(pady=(10, 20))

        title_label = tk.Label(
            title_frame,
            text="📝 تسجيل حساب جديد",
            font=self.fonts['title'],
            bg=self.colors['background'],
            fg=self.colors['primary']
        )
        title_label.pack()

        # بطاقة التسجيل
        register_card = self.create_card(main_frame)
        register_card.pack(fill=tk.BOTH, expand=True)

        # محتوى البطاقة
        card_content = tk.Frame(register_card, bg=self.colors['surface'])
        card_content.pack(fill=tk.BOTH, expand=True, padx=25, pady=20)

        # الحقول
        fields = [
            ("👤 الاسم الكامل", "full_name", False),
            ("📧 البريد الإلكتروني", "email", False),
            ("📱 رقم الهاتف", "phone", False),
            ("🔒 كلمة المرور", "password", True),
            ("🔒 تأكيد كلمة المرور", "confirm_password", True)
        ]

        self.register_entries = {}

        for label_text, field_name, is_password in fields:
            # التسمية
            label = tk.Label(
                card_content,
                text=label_text,
                font=self.fonts['body'],
                bg=self.colors['surface'],
                fg=self.colors['text']
            )
            label.pack(anchor=tk.W, pady=(5, 3))

            # حقل الإدخال
            entry = self.create_entry(
                card_content,
                show="*" if is_password else "",
                width=35
            )
            entry.pack(fill=tk.X, pady=(0, 10), ipady=6)
            self.register_entries[field_name] = entry

        # أزرار
        register_btn = self.create_button(
            card_content,
            "✅ تسجيل",
            self.register,
            style='success'
        )
        register_btn.pack(fill=tk.X, pady=(10, 8), ipady=10)

        back_btn = self.create_button(
            card_content,
            "🔙 رجوع",
            self.show_login_page,
            style='warning'
        )
        back_btn.pack(fill=tk.X, ipady=8)

    def register(self):
        """تسجيل مستخدم جديد"""
        full_name = self.register_entries['full_name'].get().strip()
        email = self.register_entries['email'].get().strip()
        phone = self.register_entries['phone'].get().strip()
        password = self.register_entries['password'].get().strip()
        confirm_password = self.register_entries['confirm_password'].get().strip()

        if not all([full_name, email, password, confirm_password]):
            messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة!")
            return

        if len(password) < 6:
            messagebox.showerror("خطأ", "كلمة المرور يجب أن تكون 6 أحرف على الأقل!")
            return

        if password != confirm_password:
            messagebox.showerror("خطأ", "كلمات المرور غير متطابقة!")
            return

        success, message = self.register_user(email, password, full_name, phone)

        if success:
            messagebox.showinfo("نجح", message)
            self.show_login_page()
        else:
            messagebox.showerror("خطأ", message)

    def show_main_page(self):
        """الصفحة الرئيسية الجميلة"""
        self.clear_window()

        # الخلفية الرئيسية
        main_frame = tk.Frame(self.root, bg=self.colors['background'])
        main_frame.pack(fill=tk.BOTH, expand=True)

        # الشريط العلوي الجميل
        header_frame = tk.Frame(main_frame, bg=self.colors['primary'], height=70)
        header_frame.pack(fill=tk.X)
        header_frame.pack_propagate(False)

        # معلومات المستخدم
        user_info = f"👋 مرحباً {self.current_user['full_name']}"
        if self.current_user['is_admin']:
            user_info += " 👑"

        user_label = tk.Label(
            header_frame,
            text=user_info,
            font=self.fonts['header'],
            bg=self.colors['primary'],
            fg='white'
        )
        user_label.pack(side=tk.LEFT, padx=20, pady=20)

        # زر تسجيل الخروج
        logout_btn = self.create_button(
            header_frame,
            "🚪 خروج",
            self.logout,
            style='error'
        )
        logout_btn.pack(side=tk.RIGHT, padx=20, pady=15, ipadx=15)

        # منطقة المحتوى
        content_frame = tk.Frame(main_frame, bg=self.colors['background'])
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # القائمة الرئيسية الجميلة
        menu_items = [
            ("🏠", "الصفحة الرئيسية", self.show_home_content, 'primary'),
            ("📝", "بلاغ جديد", self.show_new_report, 'success'),
            ("📋", "بلاغاتي", self.show_my_reports, 'warning'),
            ("💰", "المحفظة", self.show_wallet, 'primary'),
            ("👤", "الملف الشخصي", self.show_profile, 'primary')
        ]

        # إضافة لوحة تحكم الأدمن
        if self.current_user['is_admin']:
            menu_items.insert(0, ("👑", "لوحة التحكم", self.show_admin_dashboard, 'error'))

        for icon, text, command, style in menu_items:
            # إطار الزر
            btn_frame = tk.Frame(content_frame, bg=self.colors['surface'], relief=tk.FLAT, bd=1)
            btn_frame.pack(fill=tk.X, pady=6)

            # الأيقونة
            icon_label = tk.Label(
                btn_frame,
                text=icon,
                font=font.Font(size=20),
                bg=self.colors['surface'],
                fg=self.colors['primary'],
                width=3
            )
            icon_label.pack(side=tk.LEFT, padx=(15, 10), pady=15)

            # الزر
            btn = self.create_button(
                btn_frame,
                text,
                command,
                style=style
            )
            btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 15), pady=10, ipady=8)

    def show_home_content(self):
        messagebox.showinfo("الصفحة الرئيسية", "🏠 مرحباً بك في تطبيق مرشد!\n\nيمكنك الإبلاغ عن المخالفات وكسب المكافآت.")

    def show_new_report(self):
        messagebox.showinfo("بلاغ جديد", "📝 هذه الميزة قيد التطوير\n\nسيتم إضافة نموذج الإبلاغ قريباً.")

    def show_my_reports(self):
        messagebox.showinfo("بلاغاتي", "📋 هذه الميزة قيد التطوير\n\nسيتم عرض قائمة بلاغاتك هنا.")

    def show_wallet(self):
        messagebox.showinfo("المحفظة", "💰 هذه الميزة قيد التطوير\n\nسيتم عرض رصيدك والمعاملات هنا.")

    def show_profile(self):
        profile_info = f"""👤 الملف الشخصي

الاسم: {self.current_user['full_name']}
البريد: {self.current_user['email']}
النوع: {'مدير النظام' if self.current_user['is_admin'] else 'مستخدم عادي'}

هذه الميزة قيد التطوير لإضافة المزيد من التفاصيل."""

        messagebox.showinfo("الملف الشخصي", profile_info)

    def show_admin_dashboard(self):
        if not self.current_user['is_admin']:
            messagebox.showerror("خطأ", "غير مصرح لك بالوصول لهذه الصفحة!")
            return

        dashboard_info = """👑 لوحة تحكم المدير

📊 الإحصائيات:
👥 المستخدمين: 1,247
📋 البلاغات: 3,456
✅ المقبولة: 2,890
💰 المكافآت: ₺45,678

🛠️ الأدوات:
• إدارة المستخدمين
• مراجعة البلاغات
• إعدادات النظام
• التقارير والإحصائيات

هذه الميزة قيد التطوير لإضافة المزيد من الأدوات."""

        messagebox.showinfo("لوحة تحكم المدير", dashboard_info)

    def logout(self):
        result = messagebox.askyesno("تسجيل الخروج", "هل تريد تسجيل الخروج؟")
        if result:
            self.current_user = None
            self.show_login_page()

    def run(self):
        self.root.mainloop()

def main():
    print("🚀 بدء تشغيل تطبيق مرشد الجميل...")
    print("✨ تصميم جميل ومحسن")
    print("📱 نافذة GUI جميلة - بدون متصفح")
    print("🔧 حجم النافذة: 360x640")

    app = BeautifulMurshidApp()
    app.run()

if __name__ == "__main__":
    main()
