# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Reports Page
"""

import streamlit as st
from utils import (
    get_user_reports, navigate_to, display_report_card, 
    display_info_message, format_datetime
)
from config import ViolationType, ReportStatus, VIOLATION_CONFIG, STATUS_CONFIG, AppColors

def show_reports_page():
    """Display user reports page"""
    
    if not st.session_state.authenticated:
        navigate_to('login')
        return
    
    user = st.session_state.current_user
    
    # Header
    st.markdown(f"""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="color: {AppColors.PRIMARY}; margin-bottom: 0.5rem;">📋 İhbarlarım</h1>
        <p style="color: #666; font-size: 1.1rem;">Gönderdiğiniz tüm ihbarları görüntüleyin</p>
    </div>
    """, unsafe_allow_html=True)
    
    # Back button
    if st.button("⬅️ Ana Sayfa", help="Ana sayfaya dön"):
        navigate_to('home')
    
    # Get user reports
    reports = get_user_reports(user.id)
    
    if not reports:
        st.markdown("---")
        display_info_message("""
        Henüz hiç ihbar oluşturmamışsınız. 
        
        İlk ihbarınızı oluşturmak için ana sayfadaki "Şimdi İhbar Et" butonuna tıklayın!
        """)
        
        if st.button("🚀 Yeni İhbar Oluştur", use_container_width=True, type="primary"):
            navigate_to('new_report')
        
        return
    
    # Statistics summary
    st.markdown("### 📊 Özet")
    
    total_reports = len(reports)
    approved_reports = len([r for r in reports if r.status == ReportStatus.APPROVED])
    pending_reports = len([r for r in reports if r.status == ReportStatus.PENDING])
    in_review_reports = len([r for r in reports if r.status == ReportStatus.IN_REVIEW])
    rejected_reports = len([r for r in reports if r.status == ReportStatus.REJECTED])
    
    total_earnings = sum([r.reward_amount for r in reports if r.reward_amount])
    
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric("Toplam İhbar", total_reports, help="Toplam gönderilen ihbar sayısı")
    
    with col2:
        st.metric("Onaylanan", approved_reports, help="Onaylanan ihbar sayısı")
    
    with col3:
        st.metric("Bekleyen", pending_reports + in_review_reports, help="Değerlendirme bekleyen ihbar sayısı")
    
    with col4:
        st.metric("Toplam Kazanç", f"₺{total_earnings:.2f}", help="Onaylanan ihbarlardan kazanılan toplam tutar")
    
    st.markdown("---")
    
    # Filters
    st.markdown("### 🔍 Filtrele")
    
    col_filter1, col_filter2, col_filter3 = st.columns(3)
    
    with col_filter1:
        # Status filter
        status_options = ["Tümü"] + [STATUS_CONFIG[status]['name_tr'] for status in ReportStatus]
        selected_status = st.selectbox(
            "Durum:",
            status_options,
            help="İhbar durumuna göre filtrele"
        )
    
    with col_filter2:
        # Violation type filter
        violation_options = ["Tümü"] + [VIOLATION_CONFIG[vtype]['name_tr'] for vtype in ViolationType]
        selected_violation = st.selectbox(
            "İhlal Türü:",
            violation_options,
            help="İhlal türüne göre filtrele"
        )
    
    with col_filter3:
        # Sort options
        sort_options = ["En Yeni", "En Eski", "Durum", "İhlal Türü"]
        selected_sort = st.selectbox(
            "Sıralama:",
            sort_options,
            help="Sıralama türünü seç"
        )
    
    # Apply filters
    filtered_reports = reports.copy()
    
    # Filter by status
    if selected_status != "Tümü":
        status_map = {STATUS_CONFIG[status]['name_tr']: status for status in ReportStatus}
        target_status = status_map[selected_status]
        filtered_reports = [r for r in filtered_reports if r.status == target_status]
    
    # Filter by violation type
    if selected_violation != "Tümü":
        violation_map = {VIOLATION_CONFIG[vtype]['name_tr']: vtype for vtype in ViolationType}
        target_violation = violation_map[selected_violation]
        filtered_reports = [r for r in filtered_reports if r.violation_type == target_violation]
    
    # Sort reports
    if selected_sort == "En Yeni":
        filtered_reports.sort(key=lambda x: x.created_at, reverse=True)
    elif selected_sort == "En Eski":
        filtered_reports.sort(key=lambda x: x.created_at)
    elif selected_sort == "Durum":
        status_order = {ReportStatus.PENDING: 0, ReportStatus.IN_REVIEW: 1, ReportStatus.APPROVED: 2, ReportStatus.REJECTED: 3}
        filtered_reports.sort(key=lambda x: status_order.get(x.status, 4))
    elif selected_sort == "İhlal Türü":
        filtered_reports.sort(key=lambda x: VIOLATION_CONFIG[x.violation_type]['name_tr'])
    
    st.markdown("---")
    
    # Display filtered results count
    if len(filtered_reports) != len(reports):
        st.info(f"📊 {len(filtered_reports)} ihbar gösteriliyor (toplam {len(reports)} ihbar)")
    
    # Display reports
    if filtered_reports:
        st.markdown("### 📄 İhbar Listesi")
        
        for i, report in enumerate(filtered_reports):
            config = VIOLATION_CONFIG[report.violation_type]
            status_config = STATUS_CONFIG[report.status]
            
            # Create expandable report card
            with st.expander(
                f"{config['icon']} {config['name_tr']} - #{report.id} - {status_config['icon']} {status_config['name_tr']}",
                expanded=i == 0  # Expand first report by default
            ):
                
                # Report details
                col_details, col_status = st.columns([2, 1])
                
                with col_details:
                    st.markdown(f"**📝 Açıklama:**")
                    st.write(report.description)
                    
                    st.markdown(f"**📍 Konum:**")
                    st.write(report.location.address)
                    
                    st.markdown(f"**📅 Oluşturulma Tarihi:**")
                    st.write(format_datetime(report.created_at))
                    
                    if report.updated_at != report.created_at:
                        st.markdown(f"**🔄 Son Güncelleme:**")
                        st.write(format_datetime(report.updated_at))
                
                with col_status:
                    # Status badge
                    st.markdown(f"""
                    <div style="text-align: center; padding: 1rem; background-color: {status_config['color']}20; border-radius: 8px; border: 2px solid {status_config['color']}; margin-bottom: 1rem;">
                        <div style="font-size: 2rem; margin-bottom: 0.5rem;">{status_config['icon']}</div>
                        <div style="font-weight: bold; color: {status_config['color']};">{status_config['name_tr']}</div>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # Reward info
                    if report.reward_amount:
                        st.success(f"💰 Ödül: ₺{report.reward_amount:.2f}")
                    elif report.status == ReportStatus.APPROVED and report.fine_amount:
                        potential_reward = report.fine_amount * 0.20
                        st.info(f"💰 Beklenen Ödül: ₺{potential_reward:.2f}")
                
                # Officer notes
                if report.officer_notes:
                    st.markdown("**👮 Memur Notu:**")
                    st.markdown(f"""
                    <div style="padding: 1rem; background-color: #f8f9fa; border-radius: 8px; border-left: 4px solid {AppColors.INFO};">
                        {report.officer_notes}
                    </div>
                    """, unsafe_allow_html=True)
                
                # Media files
                if report.media_urls:
                    st.markdown("**📸 Ekli Dosyalar:**")
                    media_cols = st.columns(min(len(report.media_urls), 3))
                    for j, media_url in enumerate(report.media_urls[:3]):
                        with media_cols[j]:
                            if media_url.startswith('data:image'):
                                st.markdown(f"""
                                <img src="{media_url}" style="width: 100%; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);" />
                                """, unsafe_allow_html=True)
                            else:
                                st.write(f"🎥 Video dosyası")
                
                # Action buttons
                st.markdown("---")
                col_action1, col_action2 = st.columns(2)
                
                with col_action1:
                    if st.button(f"📍 Konumu Haritada Gör", key=f"map_{report.id}"):
                        st.info("🗺️ Harita özelliği yakında eklenecek!")
                
                with col_action2:
                    if st.button(f"📤 Paylaş", key=f"share_{report.id}"):
                        st.info("📤 Paylaşım özelliği yakında eklenecek!")
    
    else:
        display_info_message("Seçilen filtrelere uygun ihbar bulunamadı.")
    
    st.markdown("---")
    
    # Quick actions
    st.markdown("### ⚡ Hızlı İşlemler")
    
    col_quick1, col_quick2 = st.columns(2)
    
    with col_quick1:
        if st.button("🚀 Yeni İhbar Oluştur", use_container_width=True, type="primary"):
            navigate_to('new_report')
    
    with col_quick2:
        if st.button("💰 Cüzdanımı Gör", use_container_width=True):
            navigate_to('wallet')
    
    # Statistics by violation type
    if reports:
        st.markdown("---")
        st.markdown("### 📈 İhlal Türü İstatistikleri")
        
        violation_stats = {}
        for report in reports:
            vtype = report.violation_type
            if vtype not in violation_stats:
                violation_stats[vtype] = {'total': 0, 'approved': 0, 'earnings': 0}
            
            violation_stats[vtype]['total'] += 1
            if report.status == ReportStatus.APPROVED:
                violation_stats[vtype]['approved'] += 1
                if report.reward_amount:
                    violation_stats[vtype]['earnings'] += report.reward_amount
        
        for vtype, stats in violation_stats.items():
            config = VIOLATION_CONFIG[vtype]
            success_rate = (stats['approved'] / stats['total'] * 100) if stats['total'] > 0 else 0
            
            st.markdown(f"""
            <div style="padding: 1rem; background: white; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); margin-bottom: 0.5rem; border-left: 4px solid {config['color']};">
                <div style="display: flex; justify-content: space-between; align-items: center;">
                    <div style="display: flex; align-items: center; gap: 0.5rem;">
                        <span style="font-size: 1.5rem;">{config['icon']}</span>
                        <strong style="color: {config['color']};">{config['name_tr']}</strong>
                    </div>
                    <div style="text-align: right;">
                        <div style="font-size: 0.875rem; color: #666;">
                            {stats['total']} ihbar • {stats['approved']} onaylı • %{success_rate:.0f} başarı
                        </div>
                        <div style="font-weight: bold; color: {AppColors.SECONDARY};">
                            ₺{stats['earnings']:.2f} kazanç
                        </div>
                    </div>
                </div>
            </div>
            """, unsafe_allow_html=True)
