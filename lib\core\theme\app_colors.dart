import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors
  static const Color primary = Color(0xFF1976D2); // Blue
  static const Color primaryLight = Color(0xFF42A5F5);
  static const Color primaryDark = Color(0xFF0D47A1);
  
  // Secondary Colors
  static const Color secondary = Color(0xFF388E3C); // Green
  static const Color secondaryLight = Color(0xFF66BB6A);
  static const Color secondaryDark = Color(0xFF1B5E20);
  
  // Violation Type Colors
  static const Color trafficViolation = Color(0xFFD32F2F); // Red
  static const Color cleanlinessViolation = Color(0xFF795548); // Brown
  static const Color harassmentViolation = Color(0xFF7B1FA2); // Purple
  static const Color violenceViolation = Color(0xFFE65100); // Deep Orange
  static const Color noiseViolation = Color(0xFF455A64); // Blue Grey
  static const Color parkingViolation = Color(0xFF5D4037); // Brown
  static const Color otherViolation = Color(0xFF616161); // Grey
  
  // Status Colors
  static const Color pending = Color(0xFFFF9800); // Orange
  static const Color approved = Color(0xFF4CAF50); // Green
  static const Color rejected = Color(0xFFF44336); // Red
  static const Color inReview = Color(0xFF2196F3); // Blue
  
  // Neutral Colors
  static const Color background = Color(0xFFF5F5F5);
  static const Color surface = Color(0xFFFFFFFF);
  static const Color error = Color(0xFFD32F2F);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onBackground = Color(0xFF212121);
  static const Color onSurface = Color(0xFF212121);
  static const Color onError = Color(0xFFFFFFFF);
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121);
  static const Color textSecondary = Color(0xFF757575);
  static const Color textHint = Color(0xFFBDBDBD);
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0);
  static const Color divider = Color(0xFFBDBDBD);
}
