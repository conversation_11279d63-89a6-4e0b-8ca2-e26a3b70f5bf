# -*- coding: utf-8 -*-
"""
Mürşid - Vatandaş İhbar Uygulaması
Configuration and Constants
"""

import streamlit as st
from enum import Enum
from dataclasses import dataclass
from typing import Dict, Any

# App Configuration
APP_NAME = "Mürşid"
APP_VERSION = "1.0.0"
APP_DESCRIPTION = "Vatandaş İhbar Uygulaması"

# Colors (matching Flutter app)
class AppColors:
    # Primary Colors
    PRIMARY = "#1976D2"  # Blue
    PRIMARY_LIGHT = "#42A5F5"
    PRIMARY_DARK = "#0D47A1"

    # Secondary Colors
    SECONDARY = "#388E3C"  # Green
    SECONDARY_LIGHT = "#66BB6A"
    SECONDARY_DARK = "#1B5E20"

    # Violation Type Colors
    TRAFFIC_VIOLATION = "#D32F2F"  # Red
    CLEANLINESS_VIOLATION = "#795548"  # Brown
    HARASSMENT_VIOLATION = "#7B1FA2"  # Purple
    VIOLENCE_VIOLATION = "#E65100"  # Deep Orange
    NOISE_VIOLATION = "#455A64"  # Blue Grey
    PARKING_VIOLATION = "#5D4037"  # Brown
    OTHER_VIOLATION = "#616161"  # Grey

    # Status Colors
    PENDING = "#FF9800"  # Orange
    APPROVED = "#4CAF50"  # Green
    REJECTED = "#F44336"  # Red
    IN_REVIEW = "#2196F3"  # Blue

    # Neutral Colors
    BACKGROUND = "#F5F5F5"
    SURFACE = "#FFFFFF"
    ERROR = "#D32F2F"
    SUCCESS = "#4CAF50"
    WARNING = "#FF9800"
    INFO = "#2196F3"

# Violation Types
class ViolationType(Enum):
    TRAFFIC = "traffic"
    CLEANLINESS = "cleanliness"
    HARASSMENT = "harassment"
    VIOLENCE = "violence"
    NOISE = "noise"
    PARKING = "parking"
    OTHER = "other"

# Report Status
class ReportStatus(Enum):
    PENDING = "pending"
    IN_REVIEW = "in_review"
    APPROVED = "approved"
    REJECTED = "rejected"

# User Roles
class UserRole(Enum):
    USER = "user"
    ADMIN = "admin"
    OFFICER = "officer"

# Violation Type Configuration
VIOLATION_CONFIG = {
    ViolationType.TRAFFIC: {
        "name_tr": "Trafik İhlali",
        "icon": "🚗",
        "color": AppColors.TRAFFIC_VIOLATION,
        "description": "Trafik kuralları ihlali"
    },
    ViolationType.CLEANLINESS: {
        "name_tr": "Temizlik İhlali",
        "icon": "🗑️",
        "color": AppColors.CLEANLINESS_VIOLATION,
        "description": "Çevre temizliği ihlali"
    },
    ViolationType.HARASSMENT: {
        "name_tr": "Taciz",
        "icon": "⚠️",
        "color": AppColors.HARASSMENT_VIOLATION,
        "description": "Taciz olayı"
    },
    ViolationType.VIOLENCE: {
        "name_tr": "Şiddet",
        "icon": "🚨",
        "color": AppColors.VIOLENCE_VIOLATION,
        "description": "Şiddet olayı"
    },
    ViolationType.NOISE: {
        "name_tr": "Gürültü",
        "icon": "🔊",
        "color": AppColors.NOISE_VIOLATION,
        "description": "Gürültü kirliliği"
    },
    ViolationType.PARKING: {
        "name_tr": "Park İhlali",
        "icon": "🅿️",
        "color": AppColors.PARKING_VIOLATION,
        "description": "Park kuralları ihlali"
    },
    ViolationType.OTHER: {
        "name_tr": "Diğer",
        "icon": "📋",
        "color": AppColors.OTHER_VIOLATION,
        "description": "Diğer ihlaller"
    }
}

# Status Configuration
STATUS_CONFIG = {
    ReportStatus.PENDING: {
        "name_tr": "Beklemede",
        "icon": "⏳",
        "color": AppColors.PENDING
    },
    ReportStatus.IN_REVIEW: {
        "name_tr": "İnceleniyor",
        "icon": "🔍",
        "color": AppColors.IN_REVIEW
    },
    ReportStatus.APPROVED: {
        "name_tr": "Onaylandı",
        "icon": "✅",
        "color": AppColors.APPROVED
    },
    ReportStatus.REJECTED: {
        "name_tr": "Reddedildi",
        "icon": "❌",
        "color": AppColors.REJECTED
    }
}

# Default Settings
DEFAULT_SETTINGS = {
    "language": "tr",
    "theme": "light",
    "notifications": True,
    "location_enabled": True
}

# Reward Configuration
REWARD_PERCENTAGE = 0.20  # 20% of fine amount
MINIMUM_WITHDRAWAL = 50.0  # Minimum withdrawal amount in TL
WITHDRAWAL_FEE = 5.0  # Withdrawal fee in TL

# Demo Data Configuration
DEMO_USER = {
    "id": "demo_user_1",
    "email": "<EMAIL>",
    "full_name": "Ahmet İssam",
    "phone": "+90 ************",
    "total_earnings": 1250.0,
    "available_balance": 850.0,
    "total_reports": 12,
    "approved_reports": 8,
    "success_rate": 85
}

# Page Configuration
PAGE_CONFIG = {
    "page_title": APP_NAME,
    "page_icon": "🛡️",
    "layout": "centered",
    "initial_sidebar_state": "collapsed"
}

# CSS Styling
def get_custom_css():
    return f"""
    <style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

    /* Global Styles */
    .main {{
        font-family: 'Roboto', sans-serif;
    }}

    /* Hide Streamlit branding */
    #MainMenu {{visibility: hidden;}}
    footer {{visibility: hidden;}}
    header {{visibility: hidden;}}

    /* Custom button styles */
    .stButton > button {{
        background-color: {AppColors.PRIMARY};
        color: white;
        border: none;
        border-radius: 8px;
        padding: 0.5rem 1rem;
        font-weight: 500;
        transition: all 0.3s ease;
    }}

    .stButton > button:hover {{
        background-color: {AppColors.PRIMARY_DARK};
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    }}

    /* Card styles */
    .card {{
        background: white;
        padding: 1.5rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 1rem;
        border: 1px solid #e0e0e0;
    }}

    /* Violation type cards */
    .violation-card {{
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }}

    .violation-card:hover {{
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }}

    .violation-card.selected {{
        border-color: {AppColors.PRIMARY};
        background-color: {AppColors.PRIMARY}10;
    }}

    /* Status badges */
    .status-badge {{
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
        color: white;
    }}

    .status-pending {{ background-color: {AppColors.PENDING}; }}
    .status-in-review {{ background-color: {AppColors.IN_REVIEW}; }}
    .status-approved {{ background-color: {AppColors.APPROVED}; }}
    .status-rejected {{ background-color: {AppColors.REJECTED}; }}

    /* Navigation styles */
    .nav-container {{
        background: white;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        margin-bottom: 2rem;
    }}

    /* Metric cards */
    .metric-card {{
        background: linear-gradient(135deg, {AppColors.PRIMARY}, {AppColors.PRIMARY_LIGHT});
        color: white;
        padding: 1.5rem;
        border-radius: 12px;
        text-align: center;
        margin-bottom: 1rem;
    }}

    .metric-value {{
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }}

    .metric-label {{
        font-size: 0.875rem;
        opacity: 0.9;
    }}

    /* Form styles */
    .stTextInput > div > div > input {{
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }}

    .stTextArea > div > div > textarea {{
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }}

    .stSelectbox > div > div > select {{
        border-radius: 8px;
        border: 1px solid #e0e0e0;
    }}

    /* Success/Error messages */
    .success-message {{
        background-color: {AppColors.SUCCESS}20;
        color: {AppColors.SUCCESS};
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid {AppColors.SUCCESS};
        margin: 1rem 0;
    }}

    .error-message {{
        background-color: {AppColors.ERROR}20;
        color: {AppColors.ERROR};
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid {AppColors.ERROR};
        margin: 1rem 0;
    }}

    /* Mobile responsive - Phone optimized */
    @media (max-width: 500px) {{
        /* Container adjustments */
        .main .block-container {{
            padding-top: 1rem;
            padding-left: 0.5rem;
            padding-right: 0.5rem;
            max-width: 100%;
        }}

        /* Card adjustments */
        .card {{
            padding: 0.75rem;
            margin-bottom: 0.75rem;
            border-radius: 8px;
        }}

        /* Metric cards for phone */
        .metric-card {{
            padding: 1rem;
            margin-bottom: 0.75rem;
        }}

        .metric-value {{
            font-size: 1.25rem;
        }}

        .metric-label {{
            font-size: 0.75rem;
        }}

        /* Button adjustments */
        .stButton > button {{
            width: 100%;
            padding: 0.75rem;
            font-size: 0.9rem;
        }}

        /* Violation cards for phone */
        .violation-card {{
            padding: 0.75rem;
            margin-bottom: 0.5rem;
        }}

        /* Form elements */
        .stTextInput, .stTextArea, .stSelectbox {{
            margin-bottom: 0.5rem;
        }}

        /* Navigation adjustments */
        .nav-container {{
            padding: 0.75rem;
            margin-bottom: 1rem;
        }}

        /* Sidebar adjustments */
        .css-1d391kg {{
            padding-top: 1rem;
        }}

        /* Hide unnecessary elements on phone */
        .css-1rs6os {{
            display: none;
        }}

        /* Adjust spacing */
        .element-container {{
            margin-bottom: 0.5rem;
        }}
    }}

    /* Very small phones */
    @media (max-width: 375px) {{
        .main .block-container {{
            padding-left: 0.25rem;
            padding-right: 0.25rem;
        }}

        .card {{
            padding: 0.5rem;
        }}

        .metric-value {{
            font-size: 1rem;
        }}

        .stButton > button {{
            padding: 0.5rem;
            font-size: 0.8rem;
        }}
    }}
    </style>
    """
